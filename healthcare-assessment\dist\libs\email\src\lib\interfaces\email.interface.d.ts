export interface EmailOptions {
    to: string;
    subject: string;
    html: string;
    from?: string;
}
export interface OtpEmailData {
    email: string;
    otpCode: string;
    expiryMinutes?: number;
}
export interface WelcomeEmailData {
    email: string;
    fullName: string;
}
export interface PasswordResetEmailData {
    email: string;
    resetToken: string;
    frontendUrl?: string;
}
//# sourceMappingURL=email.interface.d.ts.map