{"name": "permissions", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/permissions/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/permissions", "main": "libs/permissions/src/index.ts", "tsConfig": "libs/permissions/tsconfig.lib.json", "assets": ["libs/permissions/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/permissions/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/permissions/jest.config.ts", "passWithNoTests": true}}}}