{"name": "identity", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/identity/src", "projectType": "application", "tags": ["scope:identity", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/identity", "main": "apps/identity/src/main.ts", "tsConfig": "apps/identity/tsconfig.app.json", "webpackConfig": "apps/identity/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "identity:build"}, "configurations": {"development": {"buildTarget": "identity:build:development"}, "production": {"buildTarget": "identity:build:production"}}}}}