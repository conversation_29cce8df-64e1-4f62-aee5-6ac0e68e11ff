import { Injectable, BadRequestException, UnauthorizedException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';
import { User, UserStatus, UserRole } from '../entities/user.entity';
import { FacilityType } from '../entities/facility-type.entity';
import { ServiceLine } from '../entities/service-line.entity';
import { EmailService } from '@app/email';
import { generateOTP } from '../utils/otp.util';
import {
  SignUpInput,
  VerifyOtpInput,
  SetPasswordInput,
  HospitalDetailsInput,
  SignInInput,
  ForgotPasswordInput,
  ResetPasswordInput,
  UpdateProfileInput,
  AuthResponse,
  SignUpResponse
} from '../dto/auth.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(FacilityType.name) private facilityTypeModel: Model<FacilityType>,
    @InjectModel(ServiceLine.name) private serviceLineModel: Model<ServiceLine>,
    private jwtService: JwtService,
    private emailService: EmailService,
  ) { }

  async signUp(signUpInput: SignUpInput): Promise<SignUpResponse> {
    const { email, facilityType, ...userData } = signUpInput;

    // Check if user already exists
    const existingUser = await this.userModel.findOne({ email });
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Validate facility type
    const facilityTypeDoc = await this.facilityTypeModel.findOne({ name: facilityType, isActive: true });
    if (!facilityTypeDoc) {
      throw new BadRequestException('Invalid facility type');
    }

    // Generate OTP
    const otpCode = generateOTP();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Create user
    const user = new this.userModel({
      ...userData,
      email,
      facilityType: facilityTypeDoc._id,
      role: UserRole.HOSPITAL_DIRECTOR, // Set default role
      status: UserStatus.PENDING_VERIFICATION,
      otpCode,
      otpExpires,
    });

    await user.save();

    // Send OTP email
    await this.emailService.sendOtpEmail(email, otpCode);

    return {
      success: true,
      message: 'Registration initiated. Please check your email for OTP verification.',
      nextStep: 'verify_otp'
    };
  }

  async verifyOtp(verifyOtpInput: VerifyOtpInput): Promise<SignUpResponse> {
    const { email, otpCode } = verifyOtpInput;

    const user = await this.userModel.findOne({ email });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.otpCode !== otpCode || user.otpExpires < new Date()) {
      throw new BadRequestException('Invalid or expired OTP');
    }

    // Clear OTP and update status
    user.otpCode = undefined;
    user.otpExpires = undefined;
    user.status = UserStatus.PENDING_PASSWORD;
    await user.save();

    return {
      success: true,
      message: 'Email verified successfully. Please set your password.',
      nextStep: 'set_password'
    };
  }

  async setPassword(setPasswordInput: SetPasswordInput): Promise<SignUpResponse> {
    const { email, password } = setPasswordInput;

    const user = await this.userModel.findOne({ email });
    if (!user || user.status !== UserStatus.PENDING_PASSWORD) {
      throw new BadRequestException('Invalid request');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    user.password = hashedPassword;
    user.status = UserStatus.ACTIVE;
    await user.save();

    // Send welcome email
    await this.emailService.sendWelcomeEmail(user.email, user.fullName);

    return {
      success: true,
      message: 'Registration completed successfully. You can now sign in.',
      nextStep: 'complete'
    };
  }

  async setHospitalDetails(hospitalDetailsInput: HospitalDetailsInput): Promise<SignUpResponse> {
    const { email, facilityName, facilityType, state, county, numberOfLicensedBeds, serviceLines } = hospitalDetailsInput;

    // Find the user
    const user = await this.userModel.findOne({ email });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Validate facility type
    const facilityTypeDoc = await this.facilityTypeModel.findOne({ name: facilityType, isActive: true });
    if (!facilityTypeDoc) {
      throw new BadRequestException('Invalid facility type');
    }

    // Validate service lines
    const serviceLineIds = [];
    if (serviceLines && serviceLines.length > 0) {
      for (const serviceLineName of serviceLines) {
        const serviceLineDoc = await this.serviceLineModel.findOne({ name: serviceLineName, isActive: true });
        if (!serviceLineDoc) {
          throw new BadRequestException(`Invalid service line: ${serviceLineName}`);
        }
        serviceLineIds.push(serviceLineDoc._id);
      }
    }

    // Update user with hospital details
    user.facilityName = facilityName;
    user.facilityType = facilityTypeDoc._id;
    user.state = state;
    user.county = county;
    user.numberOfLicensedBeds = numberOfLicensedBeds;
    user.serviceLines = serviceLineIds;
    user.status = UserStatus.PENDING_DETAILS;

    await user.save();

    return {
      success: true,
      message: 'Hospital details updated successfully.',
      nextStep: 'complete'
    };
  }

  async signIn(signInInput: SignInInput): Promise<AuthResponse> {
    const { emailOrPhone, password } = signInInput;

    // Find user by email or phone
    const user = await this.userModel.findOne({
      $or: [
        { email: emailOrPhone },
        { phoneNumber: emailOrPhone }
      ]
    }).populate('facilityType');

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.status !== UserStatus.ACTIVE) {
      throw new UnauthorizedException('Account not activated. Please complete registration.');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate JWT token
    const payload = {
      userId: user._id,
      email: user.email,
      role: user.role,
      facilityName: user.facilityName
    };

    const token = this.jwtService.sign(payload);

    return {
      success: true,
      message: 'Sign in successful',
      token,
      user
    };
  }

  async forgotPassword(forgotPasswordInput: ForgotPasswordInput): Promise<AuthResponse> {
    const { email } = forgotPasswordInput;

    const user = await this.userModel.findOne({ email });
    if (!user) {
      return {
        success: true,
        message: 'If an account with this email exists, a password reset link has been sent.'
      };
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

    user.resetToken = resetToken;
    user.resetTokenExpires = resetExpires;
    await user.save();

    // Send reset email
    await this.emailService.sendPasswordResetEmail(email, resetToken);

    return {
      success: true,
      message: 'If an account with this email exists, a password reset link has been sent.'
    };
  }

  async resetPassword(resetPasswordInput: ResetPasswordInput): Promise<AuthResponse> {
    const { token, password } = resetPasswordInput;

    const user = await this.userModel.findOne({
      resetToken: token,
      resetTokenExpires: { $gt: new Date() }
    });

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 12);

    user.password = hashedPassword;
    user.resetToken = undefined;
    user.resetTokenExpires = undefined;
    await user.save();

    return {
      success: true,
      message: 'Password reset successfully'
    };
  }

  async updateProfile(userId: string, updateProfileInput: UpdateProfileInput): Promise<AuthResponse> {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      updateProfileInput,
      { new: true }
    ).populate('facilityType');

    if (!user) {
      throw new BadRequestException('User not found');
    }

    return {
      success: true,
      message: 'Profile updated successfully',
      user
    };
  }

  async getProfile(userId: string): Promise<AuthResponse> {
    const user = await this.userModel.findById(userId).populate('facilityType');
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return {
      success: true,
      message: 'Profile retrieved successfully',
      user
    };
  }
}