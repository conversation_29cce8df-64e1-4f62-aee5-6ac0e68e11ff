{"run": {"command": "nx build", "startTime": "2025-07-31T07:44:22.574Z", "endTime": "2025-07-31T07:44:38.558Z", "inner": false}, "tasks": [{"taskId": "email:build", "target": "build", "projectName": "email", "hash": "6467598161775099768", "startTime": "2025-07-31T07:44:22.586Z", "endTime": "2025-07-31T07:44:26.886Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "db:build", "target": "build", "projectName": "db", "hash": "3798097615464351640", "startTime": "2025-07-31T07:44:22.586Z", "endTime": "2025-07-31T07:44:27.119Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "identity:build:production", "target": "build", "projectName": "identity", "hash": "18056074685766582294", "startTime": "2025-07-31T07:44:27.122Z", "endTime": "2025-07-31T07:44:38.558Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}