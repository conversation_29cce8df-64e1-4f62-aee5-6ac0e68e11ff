{"run": {"command": "nx build", "startTime": "2025-07-31T07:57:55.180Z", "endTime": "2025-07-31T07:58:12.312Z", "inner": false}, "tasks": [{"taskId": "email:build", "target": "build", "projectName": "email", "hash": "6467598161775099768", "startTime": "2025-07-31T07:57:55.193Z", "endTime": "2025-07-31T07:58:00.332Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "db:build", "target": "build", "projectName": "db", "hash": "3798097615464351640", "startTime": "2025-07-31T07:57:55.192Z", "endTime": "2025-07-31T07:58:00.417Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "identity:build:production", "target": "build", "projectName": "identity", "hash": "11192797354471991131", "startTime": "2025-07-31T07:58:00.420Z", "endTime": "2025-07-31T07:58:12.312Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}