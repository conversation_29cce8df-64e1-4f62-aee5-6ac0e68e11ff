{"run": {"command": "nx build", "startTime": "2025-07-31T08:12:58.412Z", "endTime": "2025-07-31T08:13:15.536Z", "inner": false}, "tasks": [{"taskId": "email:build", "target": "build", "projectName": "email", "hash": "6467598161775099768", "startTime": "2025-07-31T08:12:58.427Z", "endTime": "2025-07-31T08:13:02.829Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "db:build", "target": "build", "projectName": "db", "hash": "3798097615464351640", "startTime": "2025-07-31T08:12:58.427Z", "endTime": "2025-07-31T08:13:03.018Z", "params": "", "cacheStatus": "cache-miss", "status": 0}, {"taskId": "identity:build:production", "target": "build", "projectName": "identity", "hash": "15242153433394887722", "startTime": "2025-07-31T08:13:03.021Z", "endTime": "2025-07-31T08:13:15.536Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}