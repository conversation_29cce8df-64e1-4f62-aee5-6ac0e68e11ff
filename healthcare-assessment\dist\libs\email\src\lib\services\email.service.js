var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
let EmailService = class EmailService {
    transporter;
    constructor() {
        this.transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST || 'localhost',
            port: parseInt(process.env.SMTP_PORT || '587'),
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS,
            },
        });
    }
    async sendOtpEmail(email, otpCode) {
        await this.transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: 'OTP Verification - Healthcare Assessment',
            html: `
        <h2>Email Verification</h2>
        <p>Your OTP code is: <strong>${otpCode}</strong></p>
        <p>This code will expire in 10 minutes.</p>
      `,
        });
    }
    async sendWelcomeEmail(email, fullName) {
        await this.transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: 'Welcome to Healthcare Assessment',
            html: `
        <h2>Welcome ${fullName}!</h2>
        <p>Your registration has been completed successfully.</p>
        <p>You can now sign in to your account.</p>
      `,
        });
    }
    async sendPasswordResetEmail(email, resetToken) {
        const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
        await this.transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: 'Password Reset - Healthcare Assessment',
            html: `
        <h2>Password Reset Request</h2>
        <p>Click the link below to reset your password:</p>
        <a href="${resetUrl}">Reset Password</a>
        <p>This link will expire in 30 minutes.</p>
      `,
        });
    }
};
EmailService = __decorate([
    Injectable(),
    __metadata("design:paramtypes", [])
], EmailService);
export { EmailService };
//# sourceMappingURL=email.service.js.map