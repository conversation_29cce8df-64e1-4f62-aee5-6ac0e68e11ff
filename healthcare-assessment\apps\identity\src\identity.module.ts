import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';

// Entities and Schemas
import { User, UserSchema } from './entities/user.entity';
import { FacilityType, FacilityTypeSchema } from './entities/facility-type.entity';
import { ServiceLine, ServiceLineSchema } from './entities/service-line.entity';

// Services
import { AuthService } from './services/auth.service';
import { SeedService } from './services/seed.service';

// Resolvers
import { AuthResolver } from './resolvers/auth.resolver';
import { UserResolver } from './resolvers/user.resolver';

// Controllers
import { AuthController } from './controllers/auth.controller';
import { DatabaseModule } from '@app/db';  // Import the DatabaseModule here
import { EmailModule } from '@app/email';  // Import EmailModule


@Module({
  imports: [
    DatabaseModule,
    EmailModule,
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: FacilityType.name, schema: FacilityTypeSchema },
      { name: ServiceLine.name, schema: ServiceLineSchema },
    ]),
    JwtModule,
  ],
  providers: [
    AuthService,
    SeedService,
    AuthResolver,
    UserResolver,
  ],
  controllers: [AuthController],
  exports: [AuthService, SeedService],
})
export class IdentityAppModule { }
