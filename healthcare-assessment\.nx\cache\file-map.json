{"version": "6.0", "nxVersion": "17.2.8", "deps": {"@nestjs/apollo": "^12.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@types/passport-jwt": "^4.0.1", "apollo-server-express": "^3.12.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "graphql": "^16.8.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.0", "nodemailer": "^6.9.0", "otplib": "^12.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "ulid": "^2.3.0", "@nx/eslint": "17.2.8", "@nx/jest": "17.2.8", "@nx/js": "17.2.8", "@nx/nest": "17.2.8", "@nx/node": "17.2.8", "@nx/webpack": "17.2.8", "@nx/workspace": "17.2.8", "@types/bcryptjs": "^2.4.2", "@types/jest": "^29.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "~18.16.9", "@types/nodemailer": "^6.4.0", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "~8.46.0", "jest": "^29.4.1", "nx": "17.2.8", "ts-jest": "^29.1.0", "typescript": "~5.2.2"}, "pathMappings": {"@app/audit": ["libs/audit/src/index.ts"], "@app/common": ["libs/common/src/index.ts"], "@app/config": ["libs/config/src/index.ts"], "@app/db": ["libs/db/src/index.ts"], "@app/email": ["libs/email/src/index.ts"], "@app/error": ["libs/error/src/index.ts"], "@app/notification": ["libs/notification/src/index.ts"], "@app/permissions": ["libs/permissions/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": "nx.json", "hash": "15517544082799850419"}, {"file": "tsconfig.json", "hash": "10747003536474934513"}, {"file": "tsconfig.base.json", "hash": "6023066232558495197"}, {"file": ".eslintrc.json", "hash": "8058564490118573943"}, {"file": ".eslintrc.js", "hash": "10431999100348984044"}, {"file": "package.json", "hash": "13234233330262831574"}, {"file": "src/schema.gql", "hash": "15926655485457382389"}, {"file": ".prettier<PERSON>", "hash": "3949734273192683148"}, {"file": "nest-cli.json", "hash": "4463778208378843778"}, {"file": ".giti<PERSON>re", "hash": "15021907370723415264"}, {"file": "jest.preset.js", "hash": "4618981940956660382"}], "projectFileMap": {"common": [{"file": "libs/common/jest.config.ts", "hash": "14855072417691290278"}, {"file": "libs/common/project.json", "hash": "1612842126806380916"}, {"file": "libs/common/src/index.ts", "hash": "4426540559690990121"}, {"file": "libs/common/src/lib/common.module.ts", "hash": "3573493449303749313", "deps": ["npm:@nestjs/common"]}, {"file": "libs/common/tsconfig.json", "hash": "4955604424410888574"}, {"file": "libs/common/tsconfig.lib.json", "hash": "5862468039154322285"}, {"file": "libs/common/tsconfig.spec.json", "hash": "10796733258993533913"}], "db": [{"file": "libs/db/jest.config.ts", "hash": "18305803762268993593"}, {"file": "libs/db/project.json", "hash": "10780933794233723511"}, {"file": "libs/db/src/index.ts", "hash": "6269796642732595364"}, {"file": "libs/db/src/lib/database.module.ts", "hash": "9104955140448593366", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose"]}, {"file": "libs/db/src/lib/db.module.ts", "hash": "9175084780229888853", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:@nestjs/config"]}, {"file": "libs/db/src/lib/db.service.ts", "hash": "6803358787418152406", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "libs/db/src/lib/services/mongo-connection.service.ts", "hash": "7724766922395373402", "deps": ["npm:@nestjs/common"]}, {"file": "libs/db/tsconfig.json", "hash": "4955604424410888574"}, {"file": "libs/db/tsconfig.lib.json", "hash": "12687705595142657282"}, {"file": "libs/db/tsconfig.spec.json", "hash": "9615430422368760453"}], "config": [{"file": "libs/config/jest.config.ts", "hash": "5537088458737026613"}, {"file": "libs/config/project.json", "hash": "2389411382756158884"}, {"file": "libs/config/src/index.ts", "hash": "3378273077156336908"}, {"file": "libs/config/src/lib/config.module.ts", "hash": "8303653383362442303", "deps": ["npm:@nestjs/common", "npm:@nestjs/config"]}, {"file": "libs/config/tsconfig.json", "hash": "4955604424410888574"}, {"file": "libs/config/tsconfig.lib.json", "hash": "12370035925967378897"}, {"file": "libs/config/tsconfig.spec.json", "hash": "10796733258993533913"}], "audit": [{"file": "libs/audit/project.json", "hash": "9255882631319674009"}, {"file": "libs/audit/src/index.ts", "hash": "16753194859413193662"}, {"file": "libs/audit/src/lib/audit.module.ts", "hash": "8178143048993860700", "deps": ["npm:@nestjs/common"]}, {"file": "libs/audit/tsconfig.lib.json", "hash": "17339810014024732346"}], "error": [{"file": "libs/error/jest.config.ts", "hash": "18081956024422494731"}, {"file": "libs/error/project.json", "hash": "13712865882854553885"}, {"file": "libs/error/src/index.ts", "hash": "2517804193744333040"}, {"file": "libs/error/src/lib/error.module.ts", "hash": "12433707322172964939", "deps": ["npm:@nestjs/common"]}, {"file": "libs/error/src/lib/exceptions/auth.exception.ts", "hash": "15915872620843176308", "deps": ["npm:@nestjs/common"]}, {"file": "libs/error/tsconfig.json", "hash": "4955604424410888574"}, {"file": "libs/error/tsconfig.lib.json", "hash": "14599638248430489053"}, {"file": "libs/error/tsconfig.spec.json", "hash": "10796733258993533913"}], "permissions": [{"file": "libs/permissions/project.json", "hash": "5320521321623057081"}, {"file": "libs/permissions/src/index.ts", "hash": "6289496414099855821"}, {"file": "libs/permissions/src/lib/permissions.module.ts", "hash": "16503678239780466000", "deps": ["npm:@nestjs/common"]}, {"file": "libs/permissions/tsconfig.lib.json", "hash": "10184675686051113363"}], "identity": [{"file": "apps/identity/.env.example", "hash": "1840547629350774702"}, {"file": "apps/identity/Dockerfile", "hash": "13409776746187206232"}, {"file": "apps/identity/jest.config.ts", "hash": "5213175037589213874"}, {"file": "apps/identity/project.json", "hash": "12547675358202472728"}, {"file": "apps/identity/src/controllers/auth.controller.ts", "hash": "5208182493849117289", "deps": ["npm:@nestjs/common"]}, {"file": "apps/identity/src/dto/auth.dto.ts", "hash": "4462603531334841661", "deps": ["npm:@nestjs/graphql", "npm:class-validator"]}, {"file": "apps/identity/src/entities/facility-type.entity.ts", "hash": "6131772230322653170", "deps": ["npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/graphql"]}, {"file": "apps/identity/src/entities/service-line.entity.ts", "hash": "9744941534827784447", "deps": ["npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/graphql"]}, {"file": "apps/identity/src/entities/user.entity.ts", "hash": "16809943329394753524", "deps": ["npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/graphql"]}, {"file": "apps/identity/src/environments/environment.production.ts", "hash": "11401017404120469063"}, {"file": "apps/identity/src/environments/environment.ts", "hash": "7434762914495744964"}, {"file": "apps/identity/src/guards/jwt-auth.guard.ts", "hash": "2932404498994977232", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:@nestjs/graphql", "npm:@nestjs/jwt"]}, {"file": "apps/identity/src/identity.module.ts", "hash": "1198572985657046316", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:@nestjs/jwt", "npm:@nestjs/passport", "npm:@nestjs/graphql", "npm:@nestjs/apollo", "npm:path", "db", "email"]}, {"file": "apps/identity/src/main.ts", "hash": "2593921481356681951", "deps": ["npm:@nestjs/core", "npm:express"]}, {"file": "apps/identity/src/resolvers/auth.resolver.ts", "hash": "2529475907694549457", "deps": ["npm:@nestjs/graphql", "npm:@nestjs/common"]}, {"file": "apps/identity/src/resolvers/user.resolver.ts", "hash": "13915753370611730609", "deps": ["npm:@nestjs/graphql", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "apps/identity/src/services/auth.service.ts", "hash": "18108293761445084336", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:mongoose", "npm:@nestjs/jwt", "npm:bcryptjs", "npm:crypto", "email"]}, {"file": "apps/identity/src/services/seed.service.ts", "hash": "43014232606025586", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "apps/identity/src/strategies/jwt.strategy.ts", "hash": "15420527229981007391", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:passport-jwt", "npm:@nestjs/mongoose", "npm:mongoose"]}, {"file": "apps/identity/src/utils/otp.util.ts", "hash": "6480374521259245928"}, {"file": "apps/identity/src/utils/token.util.ts", "hash": "16366872206748154176", "deps": ["npm:crypto"]}, {"file": "apps/identity/tsconfig.app.json", "hash": "14889034929398493871"}, {"file": "apps/identity/tsconfig.json", "hash": "7218355892232539004"}, {"file": "apps/identity/tsconfig.spec.json", "hash": "10796733258993533913"}, {"file": "apps/identity/webpack.config.js", "hash": "16653332821218713844", "deps": ["npm:@nx/webpack"]}], "notification": [{"file": "libs/notification/project.json", "hash": "14435575195895679337"}, {"file": "libs/notification/src/index.ts", "hash": "2872383436183869393"}, {"file": "libs/notification/src/lib/notification.module.ts", "hash": "3646978136257047245", "deps": ["npm:@nestjs/common"]}, {"file": "libs/notification/tsconfig.lib.json", "hash": "11148417666158417471"}], "email": [{"file": "libs/email/jest.config.ts", "hash": "11283574658023356335"}, {"file": "libs/email/project.json", "hash": "12267451816003003487"}, {"file": "libs/email/src/index.ts", "hash": "14267557164080890653"}, {"file": "libs/email/src/lib/email.module.ts", "hash": "11385720561848604305", "deps": ["npm:@nestjs/common"]}, {"file": "libs/email/src/lib/interfaces/email.interface.ts", "hash": "3623874315164311598"}, {"file": "libs/email/src/lib/services/email.service.ts", "hash": "5741271895057384441", "deps": ["npm:@nestjs/common", "npm:nodemailer"]}, {"file": "libs/email/tsconfig.json", "hash": "4955604424410888574"}, {"file": "libs/email/tsconfig.lib.json", "hash": "8088635938268717270"}, {"file": "libs/email/tsconfig.spec.json", "hash": "10796733258993533913"}]}}}