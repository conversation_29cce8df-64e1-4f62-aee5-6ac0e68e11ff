{
  "compilerOptions": {
    "target": "ES2020", // or "ESNext"
    "sourceMap": true,
    "declaration": false,
    "moduleResolution": "node",
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@app/audit": [
        "libs/audit/src/index.ts"
      ],
      "@app/common": [
        "libs/common/src/index.ts"
      ],
      "@app/config": [
        "libs/config/src/index.ts"
      ],
      "@app/db": [
        "libs/db/src/index.ts"
      ],
      "@app/email": [
        "libs/email/src/index.ts"
      ],
      "@app/error": [
        "libs/error/src/index.ts"
      ],
      "@app/notification": [
        "libs/notification/src/index.ts"
      ],
      "@app/permissions": [
        "libs/permissions/src/index.ts"
      ]
    }
  }
}