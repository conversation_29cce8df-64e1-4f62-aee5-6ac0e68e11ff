export const environment = {
  production: false,
  port: 4001,
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/healthcare-assessment',
  jwtSecret: process.env.JWT_SECRET || 'healthcare-assessment-secret',
  emailService: {
    host: process.env.EMAIL_HOST || 'localhost',
    port: parseInt(process.env.EMAIL_PORT || '587'),
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  },
};