import { NestFactory } from '@nestjs/core';
import { IdentityAppModule } from './identity.module';  // Change IdentityModule to IdentityAppModule
import { Response } from 'express';

async function bootstrap() {
  const app = await NestFactory.create(IdentityAppModule);
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization,orgId,subOrgId,processId,userId,roleName',
  });

  // Add simple health check endpoint
  app.use('/health', (_, res: Response) => {
    res.status(200).json({ status: 'ok', uptime: process.uptime() });
  });

  await app.listen(process.env.PORT ?? 4001);
}

bootstrap();
