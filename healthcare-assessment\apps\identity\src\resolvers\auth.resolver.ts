import { Resolver, Mutation, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { 
  SignUpInput, 
  VerifyOtpInput, 
  SetPasswordInput, 
  HospitalDetailsInput,
  SignInInput,
  ForgotPasswordInput,
  ResetPasswordInput,
  UpdateProfileInput,
  AuthResponse,
  SignUpResponse 
} from '../dto/auth.dto';

@Resolver()
export class AuthResolver {
  constructor(private readonly authService: AuthService) {}

  @Mutation(() => SignUpResponse)
  async signUp(@Args('input') signUpInput: SignUpInput): Promise<SignUpResponse> {
    return this.authService.signUp(signUpInput);
  }

  @Mutation(() => SignUpResponse)
  async verifyOtp(@Args('input') verifyOtpInput: VerifyOtpInput): Promise<SignUpResponse> {
    return this.authService.verifyOtp(verifyOtpInput);
  }

  @Mutation(() => SignUpResponse)
  async setPassword(@Args('input') setPasswordInput: SetPasswordInput): Promise<SignUpResponse> {
    return this.authService.setPassword(setPasswordInput);
  }

  @Mutation(() => SignUpResponse)
  async setHospitalDetails(@Args('input') hospitalDetailsInput: HospitalDetailsInput): Promise<SignUpResponse> {
    return this.authService.setHospitalDetails(hospitalDetailsInput);
  }

  @Mutation(() => AuthResponse)
  async signIn(@Args('input') signInInput: SignInInput): Promise<AuthResponse> {
    return this.authService.signIn(signInInput);
  }

  @Mutation(() => AuthResponse)
  async forgotPassword(@Args('input') forgotPasswordInput: ForgotPasswordInput): Promise<AuthResponse> {
    return this.authService.forgotPassword(forgotPasswordInput);
  }

  @Mutation(() => AuthResponse)
  async resetPassword(@Args('input') resetPasswordInput: ResetPasswordInput): Promise<AuthResponse> {
    return this.authService.resetPassword(resetPasswordInput);
  }

  @Mutation(() => AuthResponse)
  async updateProfile(
    @Args('input') updateProfileInput: UpdateProfileInput,
    @Context() context: any
  ): Promise<AuthResponse> {
    const userId = context.req.user?.userId;
    return this.authService.updateProfile(userId, updateProfileInput);
  }

  @Query(() => AuthResponse)
  async getProfile(@Context() context: any): Promise<AuthResponse> {
    const userId = context.req.user?.userId;
    return this.authService.getProfile(userId);
  }
}