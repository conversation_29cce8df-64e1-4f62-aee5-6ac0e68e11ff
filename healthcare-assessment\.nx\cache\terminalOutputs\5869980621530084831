chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 61.4 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (9dff7c88158d984c)
[1m[31mDebugger listening on ws://localhost:9229/78b64202-2ea8-492b-95d2-5cbe07c40420[39m[22m
[1m[31mDebugger listening on ws://localhost:9229/78b64202-2ea8-492b-95d2-5cbe07c40420[39m[22m
[1m[31mFor help, see: https://nodejs.org/en/docs/inspector[39m[22m
[1m[31m[39m[22m
[1m[31m(node:11608) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.[39m[22m
[1m[31m(Use `node --trace-deprecation ...` to show where the warning was created)[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 11608  - [39m[31m07/31/2025, 1:20:16 PM [31m  ERROR[39m[31m [38;5;3m[NestApplication] [39m[31m[31mError: listen EADDRINUSE: address already in use :::4001[39m[31m[38;5;3m +3ms[39m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mError: listen EADDRINUSE: address already in use :::4001[39m[22m
[1m[31m    at Server.setupListenHandle [as _listen2] (node:net:1907:16)[39m[22m
[1m[31m    at listenInCluster (node:net:1964:12)[39m[22m
[1m[31m    at Server.listen (node:net:2066:7)[39m[22m
[1m[31m    at ExpressAdapter.listen (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\platform-express\adapters\express-adapter.js:95:32)[39m[22m
[1m[31m    at C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\core\nest-application.js:183:30[39m[22m
[1m[31m    at new Promise (<anonymous>)[39m[22m
[1m[31m    at NestApplication.listen (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\core\nest-application.js:173:16)[39m[22m
[1m[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m[22m
[1m[31m    at bootstrap (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\dist\apps\identity\main.js:1586:5)[39m[22m
[1m[31m[39m[22m

[36m>[39m [7m[1m[36m NX [39m[22m[27m [1mProcess exited with code 1, waiting for changes to restart...[22m

chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 61.4 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (b5174016a9a6509e)
[1m[31mDebugger listening on ws://localhost:9229/91b5117b-47d3-4b58-80bb-d139dad44a60[39m[22m
[1m[31mDebugger listening on ws://localhost:9229/91b5117b-47d3-4b58-80bb-d139dad44a60[39m[22m
[1m[31mFor help, see: https://nodejs.org/en/docs/inspector[39m[22m
[1m[31m[39m[22m
[1m[31m(node:29828) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.[39m[22m
[1m[31m(Use `node --trace-deprecation ...` to show where the warning was created)[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 29828  - [39m[31m07/31/2025, 1:26:39 PM [31m  ERROR[39m[31m [38;5;3m[NestApplication] [39m[31m[31mError: listen EADDRINUSE: address already in use :::4001[39m[31m[38;5;3m +7ms[39m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mError: listen EADDRINUSE: address already in use :::4001[39m[22m
[1m[31m    at Server.setupListenHandle [as _listen2] (node:net:1907:16)[39m[22m
[1m[31m    at listenInCluster (node:net:1964:12)[39m[22m
[1m[31m    at Server.listen (node:net:2066:7)[39m[22m
[1m[31m    at ExpressAdapter.listen (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\platform-express\adapters\express-adapter.js:95:32)[39m[22m
[1m[31m    at C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\core\nest-application.js:183:30[39m[22m
[1m[31m    at new Promise (<anonymous>)[39m[22m
[1m[31m    at NestApplication.listen (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\core\nest-application.js:173:16)[39m[22m
[1m[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m[22m
[1m[31m    at bootstrap (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\dist\apps\identity\main.js:1586:5)[39m[22m
[1m[31m[39m[22m

[36m>[39m [7m[1m[36m NX [39m[22m[27m [1mProcess exited with code 1, waiting for changes to restart...[22m

chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 62.8 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (c8106aed837f48d9)
[1m[31mDebugger listening on ws://localhost:9229/9af15f9f-4427-4610-8a32-619bf7345f8e[39m[22m
[1m[31mDebugger listening on ws://localhost:9229/9af15f9f-4427-4610-8a32-619bf7345f8e[39m[22m
[1m[31mFor help, see: https://nodejs.org/en/docs/inspector[39m[22m
[1m[31m[39m[22m
[1m[31m(node:3992) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.[39m[22m
[1m[31m(Use `node --trace-deprecation ...` to show where the warning was created)[39m[22m
[1m[31m[39m[22m
[1m[31m[31m[Nest] 3992  - [39m[31m07/31/2025, 1:26:54 PM [31m  ERROR[39m[31m [38;5;3m[NestApplication] [39m[31m[31mError: listen EADDRINUSE: address already in use :::4001[39m[31m[38;5;3m +3ms[39m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mError: listen EADDRINUSE: address already in use :::4001[39m[22m
[1m[31m    at Server.setupListenHandle [as _listen2] (node:net:1907:16)[39m[22m
[1m[31m    at listenInCluster (node:net:1964:12)[39m[22m
[1m[31m    at Server.listen (node:net:2066:7)[39m[22m
[1m[31m    at ExpressAdapter.listen (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\platform-express\adapters\express-adapter.js:95:32)[39m[22m
[1m[31m    at C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\core\nest-application.js:183:30[39m[22m
[1m[31m    at new Promise (<anonymous>)[39m[22m
[1m[31m    at NestApplication.listen (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\core\nest-application.js:173:16)[39m[22m
[1m[31m    at processTicksAndRejections (node:internal/process/task_queues:105:5)[39m[22m
[1m[31m    at bootstrap (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\dist\apps\identity\main.js:1616:5)[39m[22m
[1m[31m[39m[22m

[36m>[39m [7m[1m[36m NX [39m[22m[27m [1mProcess exited with code 1, waiting for changes to restart...[22m

