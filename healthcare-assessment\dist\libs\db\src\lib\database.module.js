var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoConnectionService } from './services/mongo-connection.service';
let DatabaseModule = class DatabaseModule {
};
DatabaseModule = __decorate([
    Module({
        imports: [
            MongooseModule.forRootAsync({
                useFactory: () => ({
                    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/healthcare-assessment',
                    connectionFactory: (connection) => {
                        connection.on('connected', () => {
                            console.log('Mongoose connected to MongoDB');
                        });
                        connection.on('error', (error) => {
                            console.error('Mongoose connection error:', error);
                        });
                        connection.on('disconnected', () => {
                            console.log('Mongoose disconnected from MongoDB');
                        });
                        return connection;
                    },
                }),
            }),
        ],
        providers: [MongoConnectionService],
        exports: [MongoConnectionService, MongooseModule],
    })
], DatabaseModule);
export { DatabaseModule };
//# sourceMappingURL=database.module.js.map