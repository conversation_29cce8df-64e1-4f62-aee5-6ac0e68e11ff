{"name": "error", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/error/src", "projectType": "library", "tags": ["scope:shared", "type:util"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/error", "main": "libs/error/src/index.ts", "tsConfig": "libs/error/tsconfig.lib.json", "assets": ["libs/error/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/error/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/error/jest.config.ts", "passWithNoTests": true}}}}