import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

@Injectable()
export class DbService {
  private readonly logger = new Logger(DbService.name);

  constructor(@InjectConnection() private connection: Connection) {}

  async getDbHealth(): Promise<{ status: string; connected: boolean }> {
    const connected = this.connection.readyState === 1;
    return {
      status: connected ? 'healthy' : 'unhealthy',
      connected,
    };
  }

  async closeConnection(): Promise<void> {
    await this.connection.close();
    this.logger.log('Database connection closed');
  }
}