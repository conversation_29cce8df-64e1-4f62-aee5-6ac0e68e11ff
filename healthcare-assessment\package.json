{"name": "healthcare-assessment", "version": "0.0.1", "description": "Healthcare Assessment Identity Service", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nx build", "test": "nx test", "lint": "nx lint", "serve": "nx serve", "start": "nx serve identity", "start:dev": "nx serve identity", "start:prod": "node dist/apps/identity/main"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/graphql": "^12.0.0", "@nestjs/apollo": "^12.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "mongoose": "^7.0.0", "apollo-server-express": "^3.12.0", "graphql": "^16.8.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "otplib": "^12.0.0", "nodemailer": "^6.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "ulid": "^2.3.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nx/eslint": "17.2.8", "@nx/jest": "17.2.8", "@nx/js": "17.2.8", "@nx/nest": "17.2.8", "@nx/node": "17.2.8", "@nx/webpack": "17.2.8", "@nx/workspace": "17.2.8", "@types/jest": "^29.4.0", "@types/node": "~18.16.9", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.0", "@types/nodemailer": "^6.4.0", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "~8.46.0", "jest": "^29.4.1", "nx": "17.2.8", "ts-jest": "^29.1.0", "typescript": "~5.2.2"}}