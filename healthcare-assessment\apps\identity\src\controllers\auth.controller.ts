import { Controller, Post, Get, Put, Body, UseGuards, Request } from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { UserResolver } from '../resolvers/user.resolver';
import {
  SignUpInput,
  VerifyOtpInput,
  SetPasswordInput,
  HospitalDetailsInput,
  SignInInput,
  ForgotPasswordInput,
  ResetPasswordInput,
  UpdateProfileInput
} from '../dto/auth.dto';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userResolver: UserResolver
  ) { }

  @Post('signup')
  async signUp(@Body() signUpInput: SignUpInput) {
    return this.authService.signUp(signUpInput);
  }

  @Post('verify-otp')
  async verifyOtp(@Body() verifyOtpInput: VerifyOtpInput) {
    return this.authService.verifyOtp(verifyOtpInput);
  }

  @Post('set-password')
  async setPassword(@Body() setPasswordInput: SetPasswordInput) {
    return this.authService.setPassword(setPasswordInput);
  }

  @Post('hospital-details')
  async setHospitalDetails(@Body() hospitalDetailsInput: HospitalDetailsInput) {
    return this.authService.setHospitalDetails(hospitalDetailsInput);
  }

  @Post('signin')
  async signIn(@Body() signInInput: SignInInput) {
    return this.authService.signIn(signInInput);
  }

  @Post('forgot-password')
  async forgotPassword(@Body() forgotPasswordInput: ForgotPasswordInput) {
    return this.authService.forgotPassword(forgotPasswordInput);
  }

  @Post('reset-password')
  async resetPassword(@Body() resetPasswordInput: ResetPasswordInput) {
    return this.authService.resetPassword(resetPasswordInput);
  }

  @Put('profile')
  async updateProfile(@Body() updateProfileInput: UpdateProfileInput, @Request() req: any) {
    return this.authService.updateProfile(req.user.userId, updateProfileInput);
  }

  @Get('profile')
  async getProfile(@Request() req: any) {
    return this.authService.getProfile(req.user.userId);
  }

  // New APIs for facility types and service lines
  @Get('facility-types')
  async getFacilityTypes() {
    return this.userResolver.getFacilityTypes();
  }

  @Get('service-lines')
  async getServiceLines() {
    return this.userResolver.getServiceLines();
  }
}