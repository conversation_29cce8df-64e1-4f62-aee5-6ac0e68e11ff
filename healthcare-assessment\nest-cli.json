{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/identity/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/identity/tsconfig.app.json"}, "monorepo": true, "root": "apps/identity", "projects": {"identity": {"type": "application", "root": "apps/identity", "entryFile": "main", "sourceRoot": "apps/identity/src", "compilerOptions": {"tsConfigPath": "apps/identity/tsconfig.app.json"}}, "db": {"type": "library", "root": "libs/db", "entryFile": "index", "sourceRoot": "libs/db/src", "compilerOptions": {"tsConfigPath": "libs/db/tsconfig.lib.json"}}, "email": {"type": "library", "root": "libs/email", "entryFile": "index", "sourceRoot": "libs/email/src", "compilerOptions": {"tsConfigPath": "libs/email/tsconfig.lib.json"}}, "error": {"type": "library", "root": "libs/error", "entryFile": "index", "sourceRoot": "libs/error/src", "compilerOptions": {"tsConfigPath": "libs/error/tsconfig.lib.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "config": {"type": "library", "root": "libs/config", "entryFile": "index", "sourceRoot": "libs/config/src", "compilerOptions": {"tsConfigPath": "libs/config/tsconfig.lib.json"}}}}