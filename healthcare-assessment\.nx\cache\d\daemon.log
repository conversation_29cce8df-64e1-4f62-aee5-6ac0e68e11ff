[NX Daemon Server] - 2025-07-30T14:33:48.266Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\86826d67f215d54bd99d\d.sock
[NX Daemon Server] - 2025-07-30T14:33:48.276Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (native)
[NX Daemon Server] - 2025-07-30T14:33:48.285Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-30T14:33:48.293Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-30T14:33:48.298Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-30T14:33:48.301Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-30T14:33:48.303Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-30T14:33:48.303Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-30T14:33:48.303Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-30T14:33:48.634Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-30T14:33:48.642Z - Time taken for 'total for creating and serializing project graph' 332.55050000000006ms
[NX Daemon Server] - 2025-07-30T14:33:48.644Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-30T14:33:48.644Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 333. Response time: 10.
[NX Daemon Server] - 2025-07-30T14:33:49.175Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-30T14:33:49.175Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-30T14:33:49.176Z - Handled HASH_TASKS. Handling time: 12. Response time: 1.
[NX Daemon Server] - 2025-07-30T14:34:08.015Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-30T14:34:08.016Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-30T14:34:08.016Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-30T14:34:08.033Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:01:18.338Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (sources)
[NX Daemon Server] - 2025-07-31T04:01:18.339Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (outputs)
[NX Daemon Server] - 2025-07-31T04:01:18.339Z - Server stopped because: "10800000ms of inactivity"
[NX Daemon Server] - 2025-07-31T04:19:38.869Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\86826d67f215d54bd99d\d.sock
[NX Daemon Server] - 2025-07-31T04:19:38.890Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (native)
[NX Daemon Server] - 2025-07-31T04:19:38.892Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:19:38.893Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T04:19:38.896Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:19:38.897Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T04:19:38.927Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:19:38.927Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:19:38.927Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:19:39.416Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T04:19:39.418Z - Time taken for 'total for creating and serializing project graph' 518.6608ms
[NX Daemon Server] - 2025-07-31T04:19:39.421Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T04:19:39.421Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 519. Response time: 5.
[NX Daemon Server] - 2025-07-31T04:19:40.516Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:20:30.301Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:20:30.301Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:20:30.301Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:20:30.305Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T04:20:30.306Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T04:20:30.307Z - Time taken for 'total for creating and serializing project graph' 0.32950000000710133ms
[NX Daemon Server] - 2025-07-31T04:20:30.307Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T04:20:30.307Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T04:20:30.416Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:22:07.231Z - [WATCHER]: workspace.json was modified
[NX Daemon Server] - 2025-07-31T04:22:07.293Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:22:07.353Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:22:07.355Z - [WATCHER]: workspace.json was modified
[NX Daemon Server] - 2025-07-31T04:22:07.403Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:22:07.403Z - [REQUEST]: workspace.json
[NX Daemon Server] - 2025-07-31T04:22:07.403Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:22:07.451Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:22:07.452Z - [WATCHER]: workspace.json was modified
[NX Daemon Server] - 2025-07-31T04:22:07.453Z - Time taken for 'hash changed files from watcher' 1.568200000008801ms
[NX Daemon Server] - 2025-07-31T04:22:07.666Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:22:07.666Z - [REQUEST]: workspace.json
[NX Daemon Server] - 2025-07-31T04:22:07.666Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:22:07.711Z - Time taken for 'hash changed files from watcher' 0.521100000012666ms
[NX Daemon Server] - 2025-07-31T04:22:10.742Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:22:10.742Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:22:10.743Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:22:10.746Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T04:22:10.747Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T04:22:10.749Z - Time taken for 'total for creating and serializing project graph' 0.3166999999957625ms
[NX Daemon Server] - 2025-07-31T04:22:10.752Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T04:22:10.752Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 5.
[NX Daemon Server] - 2025-07-31T04:22:10.988Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:22:11.045Z - [WATCHER]: 0 file(s) created or restored, 1 file(s) modified, 1 file(s) deleted
[NX Daemon Server] - 2025-07-31T04:22:11.046Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:22:11.152Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:22:11.152Z - [REQUEST]: tsconfig.base.json
[NX Daemon Server] - 2025-07-31T04:22:11.152Z - [REQUEST]: workspace.json
[NX Daemon Server] - 2025-07-31T04:22:11.188Z - Time taken for 'hash changed files from watcher' 0.32110000000102445ms
[NX Daemon Server] - 2025-07-31T04:24:57.531Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:24:57.531Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:24:57.532Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:24:57.537Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T04:24:57.537Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T04:24:57.538Z - Time taken for 'total for creating and serializing project graph' 0.29170000000158325ms
[NX Daemon Server] - 2025-07-31T04:24:57.540Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T04:24:57.541Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX Daemon Server] - 2025-07-31T04:24:57.614Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T04:24:57.614Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T04:24:57.614Z - Handled HASH_TASKS. Handling time: 13. Response time: 0.
[NX Daemon Server] - 2025-07-31T04:25:14.507Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T04:25:14.507Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T04:25:14.507Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T04:25:14.518Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:26:51.533Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:26:51.536Z - [WATCHER]: apps/identity/project.json was modified
[NX Daemon Server] - 2025-07-31T04:26:51.643Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:26:51.643Z - [REQUEST]: apps/identity/project.json
[NX Daemon Server] - 2025-07-31T04:26:51.643Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:26:51.880Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:26:51.883Z - [WATCHER]: apps/identity/project.json was modified
[NX Daemon Server] - 2025-07-31T04:26:51.884Z - Time taken for 'hash changed files from watcher' 1.0718999999808148ms
[NX Daemon Server] - 2025-07-31T04:26:52.096Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:26:52.096Z - [REQUEST]: apps/identity/project.json
[NX Daemon Server] - 2025-07-31T04:26:52.096Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:26:52.151Z - Time taken for 'hash changed files from watcher' 0.7992000000085682ms
[NX Daemon Server] - 2025-07-31T04:26:56.244Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:26:56.245Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:26:56.245Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:26:56.250Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T04:26:56.251Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T04:26:56.253Z - Time taken for 'total for creating and serializing project graph' 0.33480000001145527ms
[NX Daemon Server] - 2025-07-31T04:26:56.254Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T04:26:56.254Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX Daemon Server] - 2025-07-31T04:26:56.297Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T04:26:56.297Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T04:26:56.297Z - Handled HASH_TASKS. Handling time: 12. Response time: 0.
[NX Daemon Server] - 2025-07-31T04:27:11.798Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:28:42.325Z - [WATCHER]: apps/identity/tsconfig.json was modified
[NX Daemon Server] - 2025-07-31T04:28:42.326Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:28:42.434Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:28:42.434Z - [REQUEST]: apps/identity/tsconfig.json
[NX Daemon Server] - 2025-07-31T04:28:42.434Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:28:42.482Z - Time taken for 'hash changed files from watcher' 0.3895000000484288ms
[NX Daemon Server] - 2025-07-31T04:30:06.976Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:30:06.978Z - [WATCHER]: apps/identity/project.json was modified
[NX Daemon Server] - 2025-07-31T04:30:07.193Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:30:07.193Z - [REQUEST]: apps/identity/project.json
[NX Daemon Server] - 2025-07-31T04:30:07.193Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:30:07.257Z - Time taken for 'hash changed files from watcher' 0.4481000000378117ms
[NX Daemon Server] - 2025-07-31T04:30:08.681Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:30:08.683Z - [WATCHER]: apps/identity/tsconfig.json was modified
[NX Daemon Server] - 2025-07-31T04:30:09.097Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:30:09.097Z - [REQUEST]: apps/identity/tsconfig.json
[NX Daemon Server] - 2025-07-31T04:30:09.098Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:30:09.155Z - Time taken for 'hash changed files from watcher' 0.4339000000618398ms
[NX Daemon Server] - 2025-07-31T04:30:23.275Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:30:26.685Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:30:26.685Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:30:26.686Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T04:30:26.692Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T04:30:26.693Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T04:30:26.694Z - Time taken for 'total for creating and serializing project graph' 0.46810000005643815ms
[NX Daemon Server] - 2025-07-31T04:30:26.694Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T04:30:26.695Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX Daemon Server] - 2025-07-31T04:30:26.754Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T04:30:26.754Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T04:30:26.754Z - Handled HASH_TASKS. Handling time: 15. Response time: 0.
[NX Daemon Server] - 2025-07-31T04:30:28.213Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:30:29.782Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:52:19.230Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:52:19.232Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX Daemon Server] - 2025-07-31T04:52:19.361Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:52:19.361Z - [REQUEST]: apps/identity/tsconfig.json,apps/identity/project.json
[NX Daemon Server] - 2025-07-31T04:52:19.361Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:52:19.470Z - Time taken for 'hash changed files from watcher' 1.7213000000920147ms
[NX Daemon Server] - 2025-07-31T04:55:45.590Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T04:55:49.259Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:55:49.261Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 12 file(s) deleted
[NX Daemon Server] - 2025-07-31T04:55:49.462Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:55:49.463Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:55:49.463Z - [REQUEST]: apps/identity/src/entities/service-line.entity.ts,apps/identity/src/dto,apps/identity/src/dto/auth.dto.ts,apps/identity/src/resolvers,apps/identity/src/entities/facility-type.entity.ts,apps/identity/src/resolvers/auth.resolver.ts,apps/identity/src/entities,apps/identity/src/services/auth.service.ts,apps/identity/src/services/email.service.ts,apps/identity/src/entities/user.entity.ts,apps/identity/src/services/seed.service.ts,apps/identity/src/services
[NX Daemon Server] - 2025-07-31T04:55:49.504Z - Time taken for 'hash changed files from watcher' 0.29609999991953373ms
[NX Daemon Server] - 2025-07-31T04:57:42.334Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 5 file(s) deleted
[NX Daemon Server] - 2025-07-31T04:57:42.335Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:57:42.741Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:57:42.741Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:57:42.741Z - [REQUEST]: apps/identity/src/app/identity/resolvers,apps/identity/src/app/identity/services,apps/identity/src/app/identity/dto,apps/identity/src/app/identity/entities,apps/identity/src/app/identity/controllers
[NX Daemon Server] - 2025-07-31T04:57:42.789Z - Time taken for 'hash changed files from watcher' 0.5063999998383224ms
[NX Daemon Server] - 2025-07-31T04:57:47.048Z - [WATCHER]: 1 file(s) created or restored, 0 file(s) modified, 1 file(s) deleted
[NX Daemon Server] - 2025-07-31T04:57:47.049Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:57:47.156Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:57:47.156Z - [REQUEST]: apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T04:57:47.156Z - [REQUEST]: apps/identity/src/app/identity/identity.module.ts
[NX Daemon Server] - 2025-07-31T04:57:47.188Z - Error detected when creating a project graph: Failed to process project dependencies with "nx-js-graph-plugin".
"Unable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\controllers\\auth.controller.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\dto\\auth.dto.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\facility-type.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\service-line.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\user.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\resolvers\\auth.resolver.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\resolvers\\user.resolver.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\services\\auth.service.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\services\\seed.service.ts: The system cannot find the path specified. (os error 3)"
[NX Daemon Server] - 2025-07-31T04:57:47.188Z - Time taken for 'hash changed files from watcher' 0.5743999998085201ms
[NX Daemon Server] - 2025-07-31T04:57:56.538Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:57:56.539Z - [WATCHER]: apps/identity/src/app/identity was deleted
[NX Daemon Server] - 2025-07-31T04:57:56.742Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:57:56.742Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:57:56.742Z - [REQUEST]: apps/identity/src/app/identity
[NX Daemon Server] - 2025-07-31T04:57:56.786Z - Error detected when creating a project graph: Failed to process project dependencies with "nx-js-graph-plugin".
"Unable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\controllers\\auth.controller.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\dto\\auth.dto.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\facility-type.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\service-line.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\user.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\resolvers\\auth.resolver.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\resolvers\\user.resolver.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\services\\auth.service.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\services\\seed.service.ts: The system cannot find the path specified. (os error 3)"
[NX Daemon Server] - 2025-07-31T04:57:56.787Z - Time taken for 'hash changed files from watcher' 0.288399999961257ms
[NX Daemon Server] - 2025-07-31T04:57:59.372Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T04:57:59.377Z - [WATCHER]: apps/identity/src/app was deleted
[NX Daemon Server] - 2025-07-31T04:57:59.793Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T04:57:59.793Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T04:57:59.793Z - [REQUEST]: apps/identity/src/app
[NX Daemon Server] - 2025-07-31T04:57:59.818Z - Error detected when creating a project graph: Failed to process project dependencies with "nx-js-graph-plugin".
"Unable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\controllers\\auth.controller.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\dto\\auth.dto.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\facility-type.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\service-line.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\entities\\user.entity.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\resolvers\\auth.resolver.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\resolvers\\user.resolver.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\services\\auth.service.ts: The system cannot find the path specified. (os error 3)\nUnable to load C:\\Users\\<USER>\\Desktop\\Assessment\\backend\\backend\\healthcare-assessment\\apps\\identity\\src\\app\\identity\\services\\seed.service.ts: The system cannot find the path specified. (os error 3)"
[NX Daemon Server] - 2025-07-31T04:57:59.818Z - Time taken for 'hash changed files from watcher' 0.15880000032484531ms
[NX Daemon Server] - 2025-07-31T05:01:54.271Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:01:54.281Z - [WATCHER]: .gitignore was modified
[NX Daemon Server] - 2025-07-31T05:01:54.282Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (sources)
[NX Daemon Server] - 2025-07-31T05:01:54.282Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (outputs)
[NX Daemon Server] - 2025-07-31T05:01:54.282Z - Server stopped because: "Stopping the daemon the set of ignored files changed (native)"
[NX Daemon Server] - 2025-07-31T05:05:41.166Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\86826d67f215d54bd99d\d.sock
[NX Daemon Server] - 2025-07-31T05:05:41.171Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (native)
[NX Daemon Server] - 2025-07-31T05:05:41.181Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:05:41.183Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:05:41.184Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:05:41.186Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T05:05:41.186Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:05:41.186Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:05:41.187Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:05:41.595Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T05:05:41.596Z - Time taken for 'total for creating and serializing project graph' 409.274ms
[NX Daemon Server] - 2025-07-31T05:05:41.597Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T05:05:41.597Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 409. Response time: 2.
[NX Daemon Server] - 2025-07-31T05:05:41.629Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T05:05:41.629Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T05:05:41.629Z - Handled HASH_TASKS. Handling time: 3. Response time: 0.
[NX Daemon Server] - 2025-07-31T05:05:43.041Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:05:44.667Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:11:41.234Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:11:41.237Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 4 file(s) deleted
[NX Daemon Server] - 2025-07-31T05:11:41.345Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:11:41.346Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:11:41.346Z - [REQUEST]: apps/identity/src/shared,apps/identity/src/shared/utils/token.util.ts,apps/identity/src/shared/utils/otp.util.ts,apps/identity/src/shared/utils
[NX Daemon Server] - 2025-07-31T05:11:41.372Z - Time taken for 'hash changed files from watcher' 0.5813000000198372ms
[NX Daemon Server] - 2025-07-31T05:29:44.839Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:29:44.883Z - [WATCHER]: tsconfig.json was modified
[NX Daemon Server] - 2025-07-31T05:29:45.097Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:29:45.097Z - [REQUEST]: tsconfig.json
[NX Daemon Server] - 2025-07-31T05:29:45.097Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:29:45.113Z - Time taken for 'hash changed files from watcher' 0.5582000000867993ms
[NX Daemon Server] - 2025-07-31T05:29:47.215Z - [WATCHER]: tsconfig.json was modified
[NX Daemon Server] - 2025-07-31T05:29:47.215Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:29:47.619Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:29:47.619Z - [REQUEST]: tsconfig.json
[NX Daemon Server] - 2025-07-31T05:29:47.619Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:29:47.637Z - Time taken for 'hash changed files from watcher' 0.31380000011995435ms
[NX Daemon Server] - 2025-07-31T05:42:57.314Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:43:04.947Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:43:10.056Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:43:10.057Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:43:10.057Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:43:10.063Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T05:43:10.063Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T05:43:10.063Z - Time taken for 'total for creating and serializing project graph' 0.3111000000499189ms
[NX Daemon Server] - 2025-07-31T05:43:10.064Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T05:43:10.064Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T05:43:10.099Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T05:43:10.099Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T05:43:10.099Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T05:43:11.564Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:43:13.203Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:45:05.440Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:45:05.441Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX Daemon Server] - 2025-07-31T05:45:05.553Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:45:05.553Z - [REQUEST]: tsconfig.json,tsconfig.base.json
[NX Daemon Server] - 2025-07-31T05:45:05.553Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:45:05.567Z - Time taken for 'hash changed files from watcher' 0.5481000002473593ms
[NX Daemon Server] - 2025-07-31T05:45:18.850Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:45:22.055Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:45:22.056Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:45:22.056Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:45:22.061Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T05:45:22.062Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T05:45:22.062Z - Time taken for 'total for creating and serializing project graph' 0.30079999985173345ms
[NX Daemon Server] - 2025-07-31T05:45:22.062Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T05:45:22.062Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T05:45:22.100Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T05:45:22.100Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T05:45:22.100Z - Handled HASH_TASKS. Handling time: 3. Response time: 0.
[NX Daemon Server] - 2025-07-31T05:45:23.569Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:45:32.033Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:48:38.461Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:48:44.405Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:48:51.175Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:48:51.175Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:48:51.176Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:48:51.181Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T05:48:51.181Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T05:48:51.182Z - Time taken for 'total for creating and serializing project graph' 0.3264000001363456ms
[NX Daemon Server] - 2025-07-31T05:48:51.182Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T05:48:51.182Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T05:48:51.216Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T05:48:51.216Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T05:48:51.216Z - Handled HASH_TASKS. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T05:48:52.607Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:49:00.596Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:53:52.199Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T05:53:52.199Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:53:52.313Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:53:52.313Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T05:53:52.313Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:53:52.397Z - Time taken for 'hash changed files from watcher' 4.567499999888241ms
[NX Daemon Server] - 2025-07-31T05:53:53.899Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:56:30.612Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:56:30.617Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T05:56:30.831Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:56:30.831Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T05:56:30.831Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:56:30.879Z - Time taken for 'hash changed files from watcher' 0.5831999997608364ms
[NX Daemon Server] - 2025-07-31T05:56:31.095Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:20.609Z - [WATCHER]: apps/identity/src/entities/facility-type.entity.ts was modified
[NX Daemon Server] - 2025-07-31T05:57:20.610Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:21.017Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:57:21.018Z - [REQUEST]: apps/identity/src/entities/facility-type.entity.ts
[NX Daemon Server] - 2025-07-31T05:57:21.018Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:57:21.079Z - Time taken for 'hash changed files from watcher' 0.45039999997243285ms
[NX Daemon Server] - 2025-07-31T05:57:22.105Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:26.727Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:26.730Z - [WATCHER]: apps/identity/src/entities/service-line.entity.ts was modified
[NX Daemon Server] - 2025-07-31T05:57:27.540Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:57:27.540Z - [REQUEST]: apps/identity/src/entities/service-line.entity.ts
[NX Daemon Server] - 2025-07-31T05:57:27.540Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:57:27.677Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:27.678Z - Time taken for 'hash changed files from watcher' 1.3127999999560416ms
[NX Daemon Server] - 2025-07-31T05:57:31.072Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:31.074Z - [WATCHER]: apps/identity/src/entities/user.entity.ts was modified
[NX Daemon Server] - 2025-07-31T05:57:32.130Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:32.689Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:57:32.689Z - [REQUEST]: apps/identity/src/entities/user.entity.ts
[NX Daemon Server] - 2025-07-31T05:57:32.689Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:57:32.758Z - Time taken for 'hash changed files from watcher' 0.7730000000447035ms
[NX Daemon Server] - 2025-07-31T05:57:44.890Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:57:47.694Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:57:47.695Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:57:47.695Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:57:47.700Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T05:57:47.700Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T05:57:47.701Z - Time taken for 'total for creating and serializing project graph' 0.30220000026747584ms
[NX Daemon Server] - 2025-07-31T05:57:47.701Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T05:57:47.701Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T05:57:47.735Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T05:57:47.736Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T05:57:47.736Z - Handled HASH_TASKS. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T05:57:49.143Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:57:56.813Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:58:50.534Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:58:50.537Z - [WATCHER]: libs/email/src/lib/services/email.service.ts was modified
[NX Daemon Server] - 2025-07-31T05:58:50.674Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:58:50.678Z - [REQUEST]: libs/email/src/lib/services/email.service.ts
[NX Daemon Server] - 2025-07-31T05:58:50.679Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:58:50.818Z - Time taken for 'hash changed files from watcher' 17.65929999994114ms
[NX Daemon Server] - 2025-07-31T05:58:52.078Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:59:00.766Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:59:11.216Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:59:11.216Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T05:59:11.216Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T05:59:11.222Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T05:59:11.222Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T05:59:11.222Z - Time taken for 'total for creating and serializing project graph' 0.315399999730289ms
[NX Daemon Server] - 2025-07-31T05:59:11.222Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T05:59:11.222Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T05:59:11.260Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T05:59:11.260Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T05:59:11.260Z - Handled HASH_TASKS. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-31T05:59:12.723Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:59:20.037Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:59:57.721Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T05:59:57.723Z - [WATCHER]: apps/identity/src/main.ts was modified
[NX Daemon Server] - 2025-07-31T05:59:57.848Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T05:59:57.848Z - [REQUEST]: apps/identity/src/main.ts
[NX Daemon Server] - 2025-07-31T05:59:57.849Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T05:59:57.941Z - Time taken for 'hash changed files from watcher' 3.0087999999523163ms
[NX Daemon Server] - 2025-07-31T05:59:58.130Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:00:45.278Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T06:00:45.278Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:00:45.512Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:00:45.512Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T06:00:45.512Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:00:45.556Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:00:45.558Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T06:00:45.559Z - Time taken for 'hash changed files from watcher' 14.047500000335276ms
[NX Daemon Server] - 2025-07-31T06:00:45.976Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:00:45.976Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T06:00:45.976Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:00:46.029Z - Time taken for 'hash changed files from watcher' 0.43359999964013696ms
[NX Daemon Server] - 2025-07-31T06:00:47.203Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:00:51.752Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:01:02.574Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:01:02.574Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:01:02.574Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:01:02.579Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T06:01:02.580Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T06:01:02.580Z - Time taken for 'total for creating and serializing project graph' 0.29749999986961484ms
[NX Daemon Server] - 2025-07-31T06:01:02.580Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T06:01:02.580Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:01:02.614Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T06:01:02.614Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T06:01:02.614Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:01:04.011Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:01:11.352Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:03:06.965Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:03:06.968Z - [WATCHER]: libs/db/src/lib/db.module.ts was modified
[NX Daemon Server] - 2025-07-31T06:03:07.075Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:03:07.075Z - [REQUEST]: libs/db/src/lib/db.module.ts
[NX Daemon Server] - 2025-07-31T06:03:07.075Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:03:07.105Z - Time taken for 'hash changed files from watcher' 0.5598999997600913ms
[NX Daemon Server] - 2025-07-31T06:04:36.235Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:04:36.241Z - [WATCHER]: apps/identity/src/identity.module.ts was modified
[NX Daemon Server] - 2025-07-31T06:04:36.458Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:04:36.458Z - [REQUEST]: apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T06:04:36.458Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:04:36.512Z - Time taken for 'hash changed files from watcher' 3.9319000001996756ms
[NX Daemon Server] - 2025-07-31T06:04:38.028Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:06:21.191Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:06:21.193Z - [WATCHER]: apps/identity/src/identity.module.ts was modified
[NX Daemon Server] - 2025-07-31T06:06:21.595Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:06:21.595Z - [REQUEST]: apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T06:06:21.596Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:06:21.643Z - Time taken for 'hash changed files from watcher' 0.830700000282377ms
[NX Daemon Server] - 2025-07-31T06:06:22.421Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:06:22.589Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:06:26.095Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:06:26.095Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:06:26.096Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:06:26.101Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T06:06:26.101Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T06:06:26.101Z - Time taken for 'total for creating and serializing project graph' 0.2856999998912215ms
[NX Daemon Server] - 2025-07-31T06:06:26.101Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T06:06:26.101Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:06:26.136Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T06:06:26.137Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T06:06:26.137Z - Handled HASH_TASKS. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-07-31T06:06:27.651Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:06:35.712Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:15:07.078Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:15:11.066Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:15:11.067Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:15:11.068Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:15:11.079Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T06:15:11.079Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T06:15:11.079Z - Time taken for 'total for creating and serializing project graph' 0.3879999998025596ms
[NX Daemon Server] - 2025-07-31T06:15:11.079Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T06:15:11.079Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:15:11.123Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T06:15:11.123Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T06:15:11.123Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:15:13.274Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:15:23.855Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:16:14.691Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:16:18.262Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:16:18.262Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:16:18.263Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:16:18.269Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T06:16:18.269Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T06:16:18.269Z - Time taken for 'total for creating and serializing project graph' 0.25699999928474426ms
[NX Daemon Server] - 2025-07-31T06:16:18.270Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T06:16:18.270Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T06:16:18.308Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T06:16:18.308Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T06:16:18.308Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:16:19.852Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:16:27.821Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:22:29.833Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T06:22:29.834Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:22:29.937Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:22:29.937Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T06:22:29.938Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:22:30.120Z - Time taken for 'hash changed files from watcher' 0.6217999998480082ms
[NX Daemon Server] - 2025-07-31T06:22:32.306Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:22:50.250Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:22:50.254Z - [WATCHER]: apps/identity/src/utils/otp.util.ts was modified
[NX Daemon Server] - 2025-07-31T06:22:50.463Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:22:50.464Z - [REQUEST]: apps/identity/src/utils/otp.util.ts
[NX Daemon Server] - 2025-07-31T06:22:50.464Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:22:50.517Z - Time taken for 'hash changed files from watcher' 0.48829999938607216ms
[NX Daemon Server] - 2025-07-31T06:22:50.689Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:23:04.247Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:23:04.251Z - [WATCHER]: apps/identity/src/controllers/auth.controller.ts was modified
[NX Daemon Server] - 2025-07-31T06:23:04.670Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:23:04.670Z - [REQUEST]: apps/identity/src/controllers/auth.controller.ts
[NX Daemon Server] - 2025-07-31T06:23:04.670Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:23:04.716Z - Time taken for 'hash changed files from watcher' 1.1463999999687076ms
[NX Daemon Server] - 2025-07-31T06:23:05.836Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:23:18.830Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:23:24.820Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:23:24.821Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:23:24.822Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:23:24.827Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T06:23:24.827Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T06:23:24.828Z - Time taken for 'total for creating and serializing project graph' 0.3113000001758337ms
[NX Daemon Server] - 2025-07-31T06:23:24.828Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T06:23:24.828Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T06:23:24.876Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T06:23:24.876Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T06:23:24.876Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:23:26.922Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:23:37.047Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:25:52.420Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:25:52.471Z - [WATCHER]: apps/identity/src/dto/auth.dto.ts was modified
[NX Daemon Server] - 2025-07-31T06:25:52.578Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:25:52.578Z - [REQUEST]: apps/identity/src/dto/auth.dto.ts
[NX Daemon Server] - 2025-07-31T06:25:52.578Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:25:53.517Z - Time taken for 'hash changed files from watcher' 1.781000000424683ms
[NX Daemon Server] - 2025-07-31T06:25:55.028Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:26:46.319Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:26:46.321Z - [WATCHER]: apps/identity/src/entities/user.entity.ts was modified
[NX Daemon Server] - 2025-07-31T06:26:46.524Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:26:46.524Z - [REQUEST]: apps/identity/src/entities/user.entity.ts
[NX Daemon Server] - 2025-07-31T06:26:46.524Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:26:46.570Z - Time taken for 'hash changed files from watcher' 0.538399999961257ms
[NX Daemon Server] - 2025-07-31T06:26:48.121Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:26:51.514Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:27:04.411Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:27:04.411Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T06:27:04.412Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T06:27:04.417Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T06:27:04.417Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T06:27:04.417Z - Time taken for 'total for creating and serializing project graph' 0.2872000001370907ms
[NX Daemon Server] - 2025-07-31T06:27:04.417Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T06:27:04.417Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:27:04.789Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T06:27:04.789Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T06:27:04.789Z - Handled HASH_TASKS. Handling time: 3. Response time: 0.
[NX Daemon Server] - 2025-07-31T06:27:20.857Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:27:52.452Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:28:08.373Z - [WATCHER]: apps/identity/src/entities/user.entity.ts was modified
[NX Daemon Server] - 2025-07-31T06:28:08.374Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T06:28:08.485Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T06:28:08.485Z - [REQUEST]: apps/identity/src/entities/user.entity.ts
[NX Daemon Server] - 2025-07-31T06:28:08.485Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T06:28:08.540Z - Time taken for 'hash changed files from watcher' 4.161199999973178ms
[NX Daemon Server] - 2025-07-31T06:28:10.087Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:06:50.069Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:06:50.069Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:06:50.070Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:06:50.080Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:06:50.081Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:06:50.081Z - Time taken for 'total for creating and serializing project graph' 0.5027999999001622ms
[NX Daemon Server] - 2025-07-31T07:06:50.081Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:06:50.081Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:06:50.288Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:06:50.288Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:06:50.288Z - Handled HASH_TASKS. Handling time: 12. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:06:54.521Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:06:54.576Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:06:54.615Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:06:54.615Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:06:54.615Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:06:54.761Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:06:54.823Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:06:54.874Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:06:54.874Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:06:54.874Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:06:54.886Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:06:56.896Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:07:04.863Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:07:04.863Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:07:04.863Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:07:04.863Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:07:04.872Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:07:22.244Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:07:22.245Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:07:22.245Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:07:22.250Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:07:22.250Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:07:22.251Z - Time taken for 'total for creating and serializing project graph' 0.31160000059753656ms
[NX Daemon Server] - 2025-07-31T07:07:22.252Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:07:22.252Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX Daemon Server] - 2025-07-31T07:07:22.259Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:07:31.078Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:07:31.078Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:07:31.078Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:07:31.084Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:07:31.084Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:07:31.084Z - Time taken for 'total for creating and serializing project graph' 0.28540000040084124ms
[NX Daemon Server] - 2025-07-31T07:07:31.084Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:07:31.084Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:07:31.097Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:14:49.208Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:14:53.944Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:14:53.944Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:14:53.945Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:14:53.950Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:14:53.951Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:14:53.951Z - Time taken for 'total for creating and serializing project graph' 0.39499999955296516ms
[NX Daemon Server] - 2025-07-31T07:14:53.951Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:14:53.951Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:14:53.985Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:14:53.985Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:14:53.985Z - Handled HASH_TASKS. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:14:55.575Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:15:03.305Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:16:06.047Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:16:33.456Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:16:39.813Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:16:39.813Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:16:39.814Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:16:39.819Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:16:39.819Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:16:39.820Z - Time taken for 'total for creating and serializing project graph' 0.3070999998599291ms
[NX Daemon Server] - 2025-07-31T07:16:39.820Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:16:39.820Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:16:39.856Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:16:39.856Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:16:39.856Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:16:41.309Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:16:48.745Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:18:45.194Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:18:45.197Z - [WATCHER]: apps/identity/src/dto/graphql.dto.ts was created or restored
[NX Daemon Server] - 2025-07-31T07:18:45.305Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:18:45.305Z - [REQUEST]: apps/identity/src/dto/graphql.dto.ts
[NX Daemon Server] - 2025-07-31T07:18:45.305Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:18:45.362Z - Time taken for 'hash changed files from watcher' 0.6836000001057982ms
[NX Daemon Server] - 2025-07-31T07:18:48.566Z - [WATCHER]: apps/identity/src/dto/graphql.dto.ts was modified
[NX Daemon Server] - 2025-07-31T07:18:48.566Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:18:48.767Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:18:48.767Z - [REQUEST]: apps/identity/src/dto/graphql.dto.ts
[NX Daemon Server] - 2025-07-31T07:18:48.767Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:18:48.813Z - Time taken for 'hash changed files from watcher' 0.36919999960809946ms
[NX Daemon Server] - 2025-07-31T07:19:38.330Z - [WATCHER]: apps/identity/src/resolvers/auth.resolver.ts was modified
[NX Daemon Server] - 2025-07-31T07:19:38.358Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:19:38.731Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:19:38.731Z - [REQUEST]: apps/identity/src/resolvers/auth.resolver.ts
[NX Daemon Server] - 2025-07-31T07:19:38.731Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:19:38.832Z - Time taken for 'hash changed files from watcher' 0.60219999961555ms
[NX Daemon Server] - 2025-07-31T07:19:40.727Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:23:05.239Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:23:05.252Z - [WATCHER]: apps/identity/src/dto/graphql.dto.ts was deleted
[NX Daemon Server] - 2025-07-31T07:23:05.558Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:23:05.560Z - [WATCHER]: apps/identity/src/resolvers/auth.resolver.ts was created or restored
[NX Daemon Server] - 2025-07-31T07:23:06.059Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:23:06.060Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:23:06.060Z - [REQUEST]: apps/identity/src/resolvers/auth.resolver.ts
[NX Daemon Server] - 2025-07-31T07:23:06.060Z - [REQUEST]: apps/identity/src/dto/graphql.dto.ts
[NX Daemon Server] - 2025-07-31T07:23:06.132Z - Time taken for 'hash changed files from watcher' 0.5092000002041459ms
[NX Daemon Server] - 2025-07-31T07:23:09.616Z - [WATCHER]: apps/identity/src/resolvers/auth.resolver.ts was modified
[NX Daemon Server] - 2025-07-31T07:23:09.618Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:23:09.825Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:23:09.825Z - [REQUEST]: apps/identity/src/resolvers/auth.resolver.ts
[NX Daemon Server] - 2025-07-31T07:23:09.825Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:23:09.878Z - Time taken for 'hash changed files from watcher' 0.40660000033676624ms
[NX Daemon Server] - 2025-07-31T07:23:10.122Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:23:15.800Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:23:15.811Z - [WATCHER]: apps/identity/src/resolvers/auth.resolver.ts was created or restored
[NX Daemon Server] - 2025-07-31T07:23:15.937Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:23:15.937Z - [REQUEST]: apps/identity/src/resolvers/auth.resolver.ts
[NX Daemon Server] - 2025-07-31T07:23:15.937Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:23:16.027Z - Time taken for 'hash changed files from watcher' 0.7800000002607703ms
[NX Daemon Server] - 2025-07-31T07:23:16.434Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:32.366Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:32.376Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX Daemon Server] - 2025-07-31T07:34:32.378Z - [WATCHER]: apps/identity/src/dto/auth.dto.ts was modified
[NX Daemon Server] - 2025-07-31T07:34:32.378Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:32.712Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:32.716Z - [WATCHER]: apps/identity/src/identity.module.ts was modified
[NX Daemon Server] - 2025-07-31T07:34:33.030Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:34:33.030Z - [REQUEST]: apps/identity/src/services/auth.service.ts,apps/identity/src/dto/auth.dto.ts,apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T07:34:33.030Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:34:33.487Z - Time taken for 'hash changed files from watcher' 313.3134000003338ms
[NX Daemon Server] - 2025-07-31T07:34:36.306Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:44.480Z - [WATCHER]: 3 file(s) created or restored, 0 file(s) modified, 0 file(s) deleted
[NX Daemon Server] - 2025-07-31T07:34:44.481Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:44.490Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:44.797Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:34:44.798Z - [REQUEST]: apps/identity/src/dto/auth.dto.ts,apps/identity/src/services/auth.service.ts,apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T07:34:44.798Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:34:45.098Z - Time taken for 'hash changed files from watcher' 89.74479999952018ms
[NX Daemon Server] - 2025-07-31T07:34:48.410Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:34:50.256Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:35:12.667Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T07:35:12.668Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:35:12.881Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:35:12.881Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T07:35:12.881Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:35:13.352Z - Time taken for 'hash changed files from watcher' 0.9534999988973141ms
[NX Daemon Server] - 2025-07-31T07:35:14.480Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:35:18.272Z - [WATCHER]: apps/identity/src/services/auth.service.ts was created or restored
[NX Daemon Server] - 2025-07-31T07:35:18.273Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:35:18.414Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:35:18.414Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T07:35:18.414Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:35:18.514Z - Time taken for 'hash changed files from watcher' 0.5497999992221594ms
[NX Daemon Server] - 2025-07-31T07:35:20.200Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:35:20.547Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:35:20.547Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:35:20.547Z - Handled RECORD_OUTPUTS_HASH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:35:20.581Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:35:55.745Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:35:55.746Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:35:55.747Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:35:55.748Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:35:55.748Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:35:55.752Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:35:55.753Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:35:55.753Z - Time taken for 'total for creating and serializing project graph' 0.45209999941289425ms
[NX Daemon Server] - 2025-07-31T07:35:55.753Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:35:55.753Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:35:55.758Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:35:55.759Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:35:55.759Z - Handled REQUEST_FILE_DATA. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:35:55.761Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:37:26.087Z - [WATCHER]: apps/identity/src/identity.module.ts was modified
[NX Daemon Server] - 2025-07-31T07:37:26.087Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:37:26.196Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:37:26.196Z - [REQUEST]: apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T07:37:26.196Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:37:26.227Z - Time taken for 'hash changed files from watcher' 0.43969999998807907ms
[NX Daemon Server] - 2025-07-31T07:37:27.149Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:37:27.150Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:37:27.150Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:37:27.151Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:37:27.151Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:37:27.153Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:37:27.154Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:37:27.154Z - Time taken for 'total for creating and serializing project graph' 0.34769999980926514ms
[NX Daemon Server] - 2025-07-31T07:37:27.154Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:37:27.154Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:37:27.158Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:37:27.159Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:37:27.159Z - Handled REQUEST_FILE_DATA. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:37:27.160Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:37:40.880Z - [WATCHER]: apps/identity/src/identity.module.ts was modified
[NX Daemon Server] - 2025-07-31T07:37:40.881Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:37:40.990Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:37:40.990Z - [REQUEST]: apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T07:37:40.990Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:37:41.030Z - Time taken for 'hash changed files from watcher' 0.6476000007241964ms
[NX Daemon Server] - 2025-07-31T07:37:41.892Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:37:41.892Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:37:41.892Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:37:41.893Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:37:41.893Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:37:41.895Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:37:41.895Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:37:41.895Z - Time taken for 'total for creating and serializing project graph' 0.28700000047683716ms
[NX Daemon Server] - 2025-07-31T07:37:41.895Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:37:41.895Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:37:41.897Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:37:41.898Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:37:41.898Z - Handled REQUEST_FILE_DATA. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:37:41.899Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:38:21.651Z - [WATCHER]: apps/identity/src/guards/jwt-auth.guard.ts was created or restored
[NX Daemon Server] - 2025-07-31T07:38:21.651Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:38:21.755Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:38:21.756Z - [REQUEST]: apps/identity/src/guards/jwt-auth.guard.ts
[NX Daemon Server] - 2025-07-31T07:38:21.756Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:38:21.824Z - Time taken for 'hash changed files from watcher' 0.5966999996453524ms
[NX Daemon Server] - 2025-07-31T07:38:22.658Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:22.658Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:38:22.659Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:22.659Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:38:22.659Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:22.660Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:38:22.660Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:38:22.660Z - Time taken for 'total for creating and serializing project graph' 0.1734000016003847ms
[NX Daemon Server] - 2025-07-31T07:38:22.660Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:38:22.661Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:38:22.662Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:38:22.662Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:38:22.662Z - Handled REQUEST_FILE_DATA. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:38:22.663Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:38:31.033Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:38:31.035Z - [WATCHER]: apps/identity/src/strategies/jwt.strategy.ts was created or restored
[NX Daemon Server] - 2025-07-31T07:38:31.142Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:38:31.142Z - [REQUEST]: apps/identity/src/strategies/jwt.strategy.ts
[NX Daemon Server] - 2025-07-31T07:38:31.142Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:38:31.176Z - Time taken for 'hash changed files from watcher' 0.42710000090301037ms
[NX Daemon Server] - 2025-07-31T07:38:32.048Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:32.048Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:38:32.048Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:32.048Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:38:32.048Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:32.050Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:38:32.050Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:38:32.051Z - Time taken for 'total for creating and serializing project graph' 0.2301000002771616ms
[NX Daemon Server] - 2025-07-31T07:38:32.051Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:38:32.051Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:38:32.053Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:38:32.053Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:38:32.053Z - Handled REQUEST_FILE_DATA. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:38:32.054Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:38:44.664Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:38:44.666Z - [WATCHER]: apps/identity/src/identity.module.ts was modified
[NX Daemon Server] - 2025-07-31T07:38:44.773Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:38:44.773Z - [REQUEST]: apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T07:38:44.773Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:38:44.813Z - Time taken for 'hash changed files from watcher' 0.5307000000029802ms
[NX Daemon Server] - 2025-07-31T07:38:45.677Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:45.678Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:38:45.678Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:45.678Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:38:45.678Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:38:45.679Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:38:45.679Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:38:45.680Z - Time taken for 'total for creating and serializing project graph' 0.17870000004768372ms
[NX Daemon Server] - 2025-07-31T07:38:45.680Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:38:45.680Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:38:45.681Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:38:45.681Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:38:45.681Z - Handled REQUEST_FILE_DATA. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:38:45.682Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:00.272Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:39:00.273Z - [WATCHER]: apps/identity/src/identity.module.ts was modified
[NX Daemon Server] - 2025-07-31T07:39:00.380Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:39:00.380Z - [REQUEST]: apps/identity/src/identity.module.ts
[NX Daemon Server] - 2025-07-31T07:39:00.380Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:39:00.416Z - Time taken for 'hash changed files from watcher' 0.3804999999701977ms
[NX Daemon Server] - 2025-07-31T07:39:01.280Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:01.281Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:01.281Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:01.281Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:39:01.281Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:01.283Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:39:01.283Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:39:01.283Z - Time taken for 'total for creating and serializing project graph' 0.1683999989181757ms
[NX Daemon Server] - 2025-07-31T07:39:01.283Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:39:01.284Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:39:01.285Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:39:01.286Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:39:01.286Z - Handled REQUEST_FILE_DATA. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:39:01.287Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:13.440Z - [WATCHER]: apps/identity/src/resolvers/auth.resolver.ts was modified
[NX Daemon Server] - 2025-07-31T07:39:13.441Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:39:13.548Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:39:13.548Z - [REQUEST]: apps/identity/src/resolvers/auth.resolver.ts
[NX Daemon Server] - 2025-07-31T07:39:13.548Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:39:13.580Z - Time taken for 'hash changed files from watcher' 0.5258000008761883ms
[NX Daemon Server] - 2025-07-31T07:39:14.463Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:14.464Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:14.464Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:14.465Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:39:14.465Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:14.468Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:39:14.469Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:39:14.469Z - Time taken for 'total for creating and serializing project graph' 0.49859999865293503ms
[NX Daemon Server] - 2025-07-31T07:39:14.469Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:39:14.469Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:39:14.472Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:39:14.472Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:39:14.472Z - Handled REQUEST_FILE_DATA. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:39:14.473Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:27.463Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:39:27.465Z - [WATCHER]: apps/identity/src/resolvers/auth.resolver.ts was modified
[NX Daemon Server] - 2025-07-31T07:39:27.573Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:39:27.573Z - [REQUEST]: apps/identity/src/resolvers/auth.resolver.ts
[NX Daemon Server] - 2025-07-31T07:39:27.573Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:39:27.604Z - Time taken for 'hash changed files from watcher' 0.5135999992489815ms
[NX Daemon Server] - 2025-07-31T07:39:28.476Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:28.477Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:28.477Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:28.478Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:39:28.478Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:28.481Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:39:28.481Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:39:28.482Z - Time taken for 'total for creating and serializing project graph' 0.391100000590086ms
[NX Daemon Server] - 2025-07-31T07:39:28.482Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:39:28.482Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:39:28.485Z - [REQUEST]: Responding to the client. handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:39:28.486Z - Done responding to the client handleRequestFileData
[NX Daemon Server] - 2025-07-31T07:39:28.486Z - Handled REQUEST_FILE_DATA. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:39:28.486Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:47.917Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:39:47.919Z - [WATCHER]: package.json was modified
[NX Daemon Server] - 2025-07-31T07:39:48.038Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:39:48.038Z - [REQUEST]: package.json
[NX Daemon Server] - 2025-07-31T07:39:48.038Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:39:48.073Z - Time taken for 'hash changed files from watcher' 0.8700999990105629ms
[NX Daemon Server] - 2025-07-31T07:39:48.543Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:39:48.938Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:48.939Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:39:48.939Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:48.940Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:39:48.941Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:39:48.943Z - [REQUEST]: Responding to the client with an error. Lock files changed LOCK-FILES-CHANGED
{ name: '', message: 'LOCK-FILES-CHANGED' }
[NX Daemon Server] - 2025-07-31T07:39:48.965Z - Done responding to the client null
[NX Daemon Server] - 2025-07-31T07:40:21.228Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\86826d67f215d54bd99d\d.sock
[NX Daemon Server] - 2025-07-31T07:40:21.233Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment (native)
[NX Daemon Server] - 2025-07-31T07:40:21.236Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:40:21.237Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:40:21.239Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:40:21.241Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:40:21.248Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:40:21.248Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:40:21.248Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:40:21.486Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:40:21.487Z - Time taken for 'total for creating and serializing project graph' 244.92240000000004ms
[NX Daemon Server] - 2025-07-31T07:40:21.489Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:40:21.489Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 245. Response time: 4.
[NX Daemon Server] - 2025-07-31T07:40:21.548Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:40:21.548Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:40:21.548Z - Handled HASH_TASKS. Handling time: 6. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:40:22.735Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:25.496Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:25.560Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:25.596Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:40:25.597Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:40:25.597Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:40:25.620Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:25.682Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:25.764Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:25.811Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:40:25.811Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:40:25.811Z - Handled RECORD_OUTPUTS_HASH. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:40:27.739Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:36.792Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:40:36.796Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:40:36.796Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:40:36.797Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:40:36.805Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:41:36.064Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:41:36.065Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:41:36.066Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:41:36.075Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:41:36.076Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:41:36.077Z - Time taken for 'total for creating and serializing project graph' 0.9456000000063796ms
[NX Daemon Server] - 2025-07-31T07:41:36.077Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:41:36.077Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:41:36.172Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:41:36.172Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:41:36.172Z - Handled HASH_TASKS. Handling time: 5. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:41:39.912Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:41:55.044Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:41:55.045Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:41:55.045Z - Handled RECORD_OUTPUTS_HASH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:41:55.067Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:42:08.855Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:42:08.855Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:42:08.856Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:42:08.860Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:42:08.861Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:42:08.861Z - Time taken for 'total for creating and serializing project graph' 0.4155999999929918ms
[NX Daemon Server] - 2025-07-31T07:42:08.861Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:42:08.861Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:42:08.915Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:42:08.915Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:42:08.915Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:42:23.203Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:42:35.733Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:42:35.733Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:42:35.733Z - Handled RECORD_OUTPUTS_HASH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:42:35.742Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:43:05.559Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:43:43.524Z - [WATCHER]: apps/identity/src/entities/user.entity.ts was modified
[NX Daemon Server] - 2025-07-31T07:43:43.524Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:43:43.626Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:43:43.626Z - [REQUEST]: apps/identity/src/entities/user.entity.ts
[NX Daemon Server] - 2025-07-31T07:43:43.626Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:43:44.312Z - Time taken for 'hash changed files from watcher' 0.43110000001615845ms
[NX Daemon Server] - 2025-07-31T07:43:44.538Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:43:44.538Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:44:22.509Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:44:22.510Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:44:22.510Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:44:22.513Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:44:22.513Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:44:22.513Z - Time taken for 'total for creating and serializing project graph' 0.22370000000228174ms
[NX Daemon Server] - 2025-07-31T07:44:22.514Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:44:22.514Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:44:22.573Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:44:22.573Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:44:22.573Z - Handled HASH_TASKS. Handling time: 6. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:44:23.693Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:26.769Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:26.862Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:26.883Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:44:26.884Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:44:26.884Z - Handled RECORD_OUTPUTS_HASH. Handling time: 4. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:44:27.027Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:27.085Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:27.119Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:44:27.119Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:44:27.119Z - Handled RECORD_OUTPUTS_HASH. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:44:29.213Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:38.550Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:38.557Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:44:38.558Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:44:38.558Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:44:38.570Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:44:52.367Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:44:52.369Z - [WATCHER]: src/schema.gql was created or restored
[NX Daemon Server] - 2025-07-31T07:44:52.476Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:44:52.476Z - [REQUEST]: src/schema.gql
[NX Daemon Server] - 2025-07-31T07:44:52.476Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:44:52.495Z - Time taken for 'hash changed files from watcher' 0.41240000003017485ms
[NX Daemon Server] - 2025-07-31T07:44:53.379Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:44:53.380Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:49:59.323Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:49:59.323Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-07-31T07:49:59.324Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:49:59.335Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:49:59.336Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:49:59.336Z - Time taken for 'total for creating and serializing project graph' 0.7465000000083819ms
[NX Daemon Server] - 2025-07-31T07:49:59.336Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:49:59.336Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:49:59.387Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:49:59.387Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:49:59.387Z - Handled HASH_TASKS. Handling time: 2. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:50:01.824Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:50:12.344Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:50:16.821Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:50:16.823Z - [WATCHER]: src/schema.gql was modified
[NX Daemon Server] - 2025-07-31T07:50:16.929Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:50:16.929Z - [REQUEST]: src/schema.gql
[NX Daemon Server] - 2025-07-31T07:50:16.929Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:50:16.956Z - Time taken for 'hash changed files from watcher' 0.5306000000564381ms
[NX Daemon Server] - 2025-07-31T07:50:17.826Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:50:17.827Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:56:20.256Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:56:20.263Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T07:56:20.501Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:56:20.501Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T07:56:20.501Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:56:20.707Z - Time taken for 'hash changed files from watcher' 1.2369999999646097ms
[NX Daemon Server] - 2025-07-31T07:56:21.292Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:56:21.292Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:56:23.543Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:56:39.520Z - [WATCHER]: src/schema.gql was modified
[NX Daemon Server] - 2025-07-31T07:56:39.521Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:56:39.929Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:56:39.929Z - [REQUEST]: src/schema.gql
[NX Daemon Server] - 2025-07-31T07:56:39.929Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:56:39.960Z - Time taken for 'hash changed files from watcher' 0.5503000000026077ms
[NX Daemon Server] - 2025-07-31T07:56:40.506Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:56:40.506Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:56:49.296Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:56:49.298Z - [WATCHER]: apps/identity/src/services/auth.service.ts was modified
[NX Daemon Server] - 2025-07-31T07:56:49.867Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:56:50.114Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:56:50.115Z - [REQUEST]: apps/identity/src/services/auth.service.ts
[NX Daemon Server] - 2025-07-31T07:56:50.115Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:56:50.170Z - Time taken for 'hash changed files from watcher' 0.5216000000946224ms
[NX Daemon Server] - 2025-07-31T07:56:50.306Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:56:50.307Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:56:54.143Z - [WATCHER]: src/schema.gql was modified
[NX Daemon Server] - 2025-07-31T07:56:54.144Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:56:55.150Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:56:55.151Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:56:55.749Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:56:55.749Z - [REQUEST]: src/schema.gql
[NX Daemon Server] - 2025-07-31T07:56:55.749Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:56:55.765Z - Time taken for 'hash changed files from watcher' 0.39669999992474914ms
[NX Daemon Server] - 2025-07-31T07:57:55.115Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:57:55.115Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:57:55.116Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:57:55.119Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-07-31T07:57:55.119Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-07-31T07:57:55.119Z - Time taken for 'total for creating and serializing project graph' 0.17749999999068677ms
[NX Daemon Server] - 2025-07-31T07:57:55.119Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-07-31T07:57:55.119Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:57:55.180Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-07-31T07:57:55.180Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-07-31T07:57:55.180Z - Handled HASH_TASKS. Handling time: 6. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:57:56.327Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:57:59.945Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:57:59.993Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:58:00.132Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:58:00.323Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:58:00.332Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:58:00.332Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:58:00.332Z - Handled RECORD_OUTPUTS_HASH. Handling time: 3. Response time: 0.
[NX Daemon Server] - 2025-07-31T07:58:00.382Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:58:00.416Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:58:00.417Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:58:00.417Z - Handled RECORD_OUTPUTS_HASH. Handling time: 2. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:58:02.560Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:58:12.312Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:58:12.312Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:58:12.313Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-07-31T07:58:12.313Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 1.
[NX Daemon Server] - 2025-07-31T07:58:12.323Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T07:59:46.467Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T07:59:46.469Z - [WATCHER]: src/schema.gql was modified
[NX Daemon Server] - 2025-07-31T07:59:46.577Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T07:59:46.577Z - [REQUEST]: src/schema.gql
[NX Daemon Server] - 2025-07-31T07:59:46.577Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T07:59:46.602Z - Time taken for 'hash changed files from watcher' 0.39259999990463257ms
[NX Daemon Server] - 2025-07-31T07:59:47.477Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T07:59:47.478Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-07-31T08:00:54.126Z - [WATCHER]: src/schema.gql was modified
[NX Daemon Server] - 2025-07-31T08:00:54.126Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-07-31T08:00:54.340Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-07-31T08:00:54.340Z - [REQUEST]: src/schema.gql
[NX Daemon Server] - 2025-07-31T08:00:54.340Z - [REQUEST]: 
[NX Daemon Server] - 2025-07-31T08:00:54.395Z - Time taken for 'hash changed files from watcher' 0.5900999999139458ms
[NX Daemon Server] - 2025-07-31T08:00:55.125Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-07-31T08:00:55.126Z - Closed a connection. Number of open connections: 1
