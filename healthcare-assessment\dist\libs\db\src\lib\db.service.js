var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DbService_1;
import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
let DbService = DbService_1 = class DbService {
    connection;
    logger = new Logger(DbService_1.name);
    constructor(connection) {
        this.connection = connection;
    }
    async getDbHealth() {
        const connected = this.connection.readyState === 1;
        return {
            status: connected ? 'healthy' : 'unhealthy',
            connected,
        };
    }
    async closeConnection() {
        await this.connection.close();
        this.logger.log('Database connection closed');
    }
};
DbService = DbService_1 = __decorate([
    Injectable(),
    __param(0, InjectConnection()),
    __metadata("design:paramtypes", [Connection])
], DbService);
export { DbService };
//# sourceMappingURL=db.service.js.map