chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 61.4 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (56d99bf49e3edef7)
[1m[31mDebugger listening on ws://localhost:9229/df6acbfb-a784-4cbb-b349-54cf6ce89db0[39m[22m
[1m[31mDebugger listening on ws://localhost:9229/df6acbfb-a784-4cbb-b349-54cf6ce89db0[39m[22m
[1m[31mFor help, see: https://nodejs.org/en/docs/inspector[39m[22m
[1m[31m[39m[22m
[1m[31m(node:15480) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.[39m[22m
[1m[31m(Use `node --trace-deprecation ...` to show where the warning was created)[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31mC:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\graphql\dist\schema-builder\factories\output-type.factory.js:19[39m[22m
[1m[31m                throw new cannot_determine_output_type_error_1.CannotDetermineOutputTypeError(hostType);[39m[22m
[1m[31m                      ^[39m[22m
[1m[31mError: Cannot determine a GraphQL output type for the "facilityType". Make sure your class is decorated with an appropriate decorator.[39m[22m
[1m[31m    at OutputTypeFactory.create (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\graphql\dist\schema-builder\factories\output-type.factory.js:19:23)[39m[22m
[1m[31m    at C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\graphql\dist\schema-builder\factories\object-type-definition.factory.js:80:53[39m[22m
[1m[31m    at Array.forEach (<anonymous>)[39m[22m
[1m[31m    at C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\@nestjs\graphql\dist\schema-builder\factories\object-type-definition.factory.js:79:24[39m[22m
[1m[31m    at resolveObjMapThunk (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\graphql\type\definition.js:504:40)[39m[22m
[1m[31m    at defineFieldMap (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\graphql\type\definition.js:766:20)[39m[22m
[1m[31m    at GraphQLObjectType._fields (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\graphql\type\definition.js:691:26)[39m[22m
[1m[31m    at GraphQLObjectType.getFields (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\graphql\type\definition.js:710:27)[39m[22m
[1m[31m    at collectReferencedTypes (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\graphql\type\schema.js:387:51)[39m[22m
[1m[31m    at new GraphQLSchema (C:\Users\<USER>\Desktop\Assessment\backend\backend\healthcare-assessment\node_modules\graphql\type\schema.js:174:9)[39m[22m
[1m[31m[39m[22m

[36m>[39m [7m[1m[36m NX [39m[22m[27m [1mProcess exited with code 1, waiting for changes to restart...[22m

