var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DbService } from './db.service';
let DatabaseModule = class DatabaseModule {
};
DatabaseModule = __decorate([
    Module({
        imports: [
            MongooseModule.forRootAsync({
                imports: [ConfigModule],
                useFactory: async (configService) => ({
                    uri: configService.get('MONGODB_URI', 'mongodb://localhost:27017/healthcare-assessment'),
                    useNewUrlParser: true,
                    useUnifiedTopology: true,
                }),
                inject: [ConfigService],
            }),
        ],
        providers: [DbService],
        exports: [DbService, MongooseModule], // Ensure MongooseModule is exported
    })
], DatabaseModule);
export { DatabaseModule };
//# sourceMappingURL=db.module.js.map