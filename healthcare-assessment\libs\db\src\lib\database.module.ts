import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MongoConnectionService } from './services/mongo-connection.service';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      useFactory: () => ({
        uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/healthcare-assessment',
        connectionFactory: (connection) => {
          connection.on('connected', () => {
            console.log('Mongoose connected to MongoDB');
          });
          connection.on('error', (error: Error) => {
            console.error('Mongoose connection error:', error);
          });
          connection.on('disconnected', () => {
            console.log('Mongoose disconnected from MongoDB');
          });
          return connection;
        },
      }),
    }),
  ],
  providers: [MongoConnectionService],
  exports: [MongoConnectionService, MongooseModule],
})
export class DatabaseModule {}