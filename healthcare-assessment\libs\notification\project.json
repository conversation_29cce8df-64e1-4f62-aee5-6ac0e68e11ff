{"name": "notification", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/notification/src", "projectType": "library", "tags": ["scope:shared", "type:feature"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/notification", "main": "libs/notification/src/index.ts", "tsConfig": "libs/notification/tsconfig.lib.json", "assets": ["libs/notification/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/notification/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/notification/jest.config.ts", "passWithNoTests": true}}}}