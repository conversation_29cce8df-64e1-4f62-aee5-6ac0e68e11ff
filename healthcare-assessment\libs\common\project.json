{"name": "common", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/common/src", "projectType": "library", "tags": ["scope:shared", "type:util"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/common", "main": "libs/common/src/index.ts", "tsConfig": "libs/common/tsconfig.lib.json", "assets": ["libs/common/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/common/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/common/jest.config.ts", "passWithNoTests": true}}}}