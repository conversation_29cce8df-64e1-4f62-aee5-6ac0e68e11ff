import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';

export enum UserRole {
  HOSPITAL_DIRECTOR = 'Hospital Director',
  ADMINISTRATOR = 'Administrator',
  SUPER_ADMIN = 'Super Admin'
}

export enum UserStatus {
  PENDING_VERIFICATION = 'pending_verification',
  PENDING_PASSWORD = 'pending_password',
  PENDING_DETAILS = 'pending_details',
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

registerEnumType(UserRole, { name: 'UserRole' });
registerEnumType(UserStatus, { name: 'UserStatus' });

@ObjectType()
@Schema({ timestamps: true })
export class User extends Document {
  @Field(() => ID)
  // _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  fullName: string;

  @Field()
  @Prop({ required: true, unique: true })
  email: string;

  @Field()
  @Prop({ required: true })
  phoneNumber: string;

  @Field()
  @Prop({ required: true })
  facilityName: string;

  @Field(() => String)
  @Prop({ required: true, ref: 'FacilityType' })
  facilityType: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  state: string;

  @Field()
  @Prop({ required: true })
  county: string;

  @Field(() => UserRole)
  @Prop({ enum: UserRole, default: UserRole.HOSPITAL_DIRECTOR })
  role: UserRole;

  @Prop()
  password: string;

  @Field(() => UserStatus)
  @Prop({ enum: UserStatus, default: UserStatus.PENDING_VERIFICATION })
  status: UserStatus;

  @Field({ nullable: true })
  @Prop()
  numberOfLicensedBeds?: number;

  @Field(() => [String], { nullable: true })
  @Prop([{ type: Types.ObjectId, ref: 'ServiceLine' }])
  serviceLines?: Types.ObjectId[];

  @Field()
  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  emailVerificationToken?: string;

  @Prop()
  emailVerificationExpires?: Date;

  @Prop()
  passwordResetToken?: string;

  @Prop()
  passwordResetExpires?: Date;

  @Prop()
  otpCode?: string;

  @Prop()
  otpExpires?: Date;

  // Add the following properties for password reset functionality
  @Prop()
  resetToken?: string;  // Add resetToken

  @Prop()
  resetTokenExpires?: Date;  // Add resetTokenExpires

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);

