/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
var __webpack_exports__ = {};

;// external "@nestjs/core"
const core_namespaceObject = require("@nestjs/core");
;// external "@nestjs/common"
const common_namespaceObject = require("@nestjs/common");
;// external "@nestjs/mongoose"
const mongoose_namespaceObject = require("@nestjs/mongoose");
;// external "@nestjs/jwt"
const jwt_namespaceObject = require("@nestjs/jwt");
;// external "@nestjs/passport"
const passport_namespaceObject = require("@nestjs/passport");
;// external "@nestjs/graphql"
const graphql_namespaceObject = require("@nestjs/graphql");
;// external "@nestjs/apollo"
const apollo_namespaceObject = require("@nestjs/apollo");
;// external "path"
const external_path_namespaceObject = require("path");
;// external "mongoose"
const external_mongoose_namespaceObject = require("mongoose");
;// ./src/entities/user.entity.ts
var __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;



var UserRole;
(function (UserRole) {
    UserRole["HOSPITAL_DIRECTOR"] = "Hospital Director";
    UserRole["ADMINISTRATOR"] = "Administrator";
    UserRole["SUPER_ADMIN"] = "Super Admin";
})(UserRole || (UserRole = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["PENDING_VERIFICATION"] = "pending_verification";
    UserStatus["PENDING_PASSWORD"] = "pending_password";
    UserStatus["PENDING_DETAILS"] = "pending_details";
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
})(UserStatus || (UserStatus = {}));
(0,graphql_namespaceObject.registerEnumType)(UserRole, { name: 'UserRole' });
(0,graphql_namespaceObject.registerEnumType)(UserStatus, { name: 'UserStatus' });
let User = class User extends external_mongoose_namespaceObject.Document {
    fullName;
    email;
    phoneNumber;
    facilityName;
    facilityType;
    state;
    county;
    role;
    password;
    status;
    numberOfLicensedBeds;
    serviceLines;
    isActive;
    emailVerificationToken;
    emailVerificationExpires;
    passwordResetToken;
    passwordResetExpires;
    otpCode;
    otpExpires;
    // Add the following properties for password reset functionality
    resetToken; // Add resetToken
    resetTokenExpires; // Add resetTokenExpires
    createdAt;
    updatedAt;
};
__decorate([
    (0,graphql_namespaceObject.Field)(() => graphql_namespaceObject.ID)
    // _id: Types.ObjectId;
    ,
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true }),
    __metadata("design:type", String)
], User.prototype, "fullName", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true }),
    __metadata("design:type", String)
], User.prototype, "phoneNumber", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true }),
    __metadata("design:type", String)
], User.prototype, "facilityName", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(() => String),
    (0,mongoose_namespaceObject.Prop)({ required: true, ref: 'FacilityType' }),
    __metadata("design:type", typeof (_a = typeof external_mongoose_namespaceObject.Types !== "undefined" && external_mongoose_namespaceObject.Types.ObjectId) === "function" ? _a : Object)
], User.prototype, "facilityType", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true }),
    __metadata("design:type", String)
], User.prototype, "state", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true }),
    __metadata("design:type", String)
], User.prototype, "county", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(() => UserRole),
    (0,mongoose_namespaceObject.Prop)({ enum: UserRole, default: UserRole.HOSPITAL_DIRECTOR }),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(() => UserStatus),
    (0,mongoose_namespaceObject.Prop)({ enum: UserStatus, default: UserStatus.PENDING_VERIFICATION }),
    __metadata("design:type", String)
], User.prototype, "status", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", Number)
], User.prototype, "numberOfLicensedBeds", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(() => [String], { nullable: true }),
    (0,mongoose_namespaceObject.Prop)([{ type: external_mongoose_namespaceObject.Types.ObjectId, ref: 'ServiceLine' }]),
    __metadata("design:type", Array)
], User.prototype, "serviceLines", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "isActive", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", String)
], User.prototype, "emailVerificationToken", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], User.prototype, "emailVerificationExpires", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", String)
], User.prototype, "passwordResetToken", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], User.prototype, "passwordResetExpires", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", String)
], User.prototype, "otpCode", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], User.prototype, "otpExpires", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", String)
], User.prototype, "resetToken", void 0);
__decorate([
    (0,mongoose_namespaceObject.Prop)(),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], User.prototype, "resetTokenExpires", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], User.prototype, "createdAt", void 0);
__decorate([
    (0,graphql_namespaceObject.Field)(),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], User.prototype, "updatedAt", void 0);
User = __decorate([
    (0,graphql_namespaceObject.ObjectType)(),
    (0,mongoose_namespaceObject.Schema)({ timestamps: true })
], User);

const UserSchema = mongoose_namespaceObject.SchemaFactory.createForClass(User);

;// ./src/entities/facility-type.entity.ts
var facility_type_entity_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var facility_type_entity_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var facility_type_entity_a, facility_type_entity_b;



let FacilityType = class FacilityType extends external_mongoose_namespaceObject.Document {
    name;
    description;
    isActive;
    createdAt;
    updatedAt;
};
facility_type_entity_decorate([
    (0,graphql_namespaceObject.Field)(() => graphql_namespaceObject.ID)
    // _id: Types.ObjectId;
    ,
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true, unique: true }),
    facility_type_entity_metadata("design:type", String)
], FacilityType.prototype, "name", void 0);
facility_type_entity_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,mongoose_namespaceObject.Prop)(),
    facility_type_entity_metadata("design:type", String)
], FacilityType.prototype, "description", void 0);
facility_type_entity_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ default: true }),
    facility_type_entity_metadata("design:type", Boolean)
], FacilityType.prototype, "isActive", void 0);
facility_type_entity_decorate([
    (0,graphql_namespaceObject.Field)(),
    facility_type_entity_metadata("design:type", typeof (facility_type_entity_a = typeof Date !== "undefined" && Date) === "function" ? facility_type_entity_a : Object)
], FacilityType.prototype, "createdAt", void 0);
facility_type_entity_decorate([
    (0,graphql_namespaceObject.Field)(),
    facility_type_entity_metadata("design:type", typeof (facility_type_entity_b = typeof Date !== "undefined" && Date) === "function" ? facility_type_entity_b : Object)
], FacilityType.prototype, "updatedAt", void 0);
FacilityType = facility_type_entity_decorate([
    (0,graphql_namespaceObject.ObjectType)(),
    (0,mongoose_namespaceObject.Schema)({ timestamps: true })
], FacilityType);

const FacilityTypeSchema = mongoose_namespaceObject.SchemaFactory.createForClass(FacilityType);

;// ./src/entities/service-line.entity.ts
var service_line_entity_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var service_line_entity_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var service_line_entity_a, service_line_entity_b;



let ServiceLine = class ServiceLine extends external_mongoose_namespaceObject.Document {
    name;
    description;
    isActive;
    createdAt;
    updatedAt;
};
service_line_entity_decorate([
    (0,graphql_namespaceObject.Field)(() => graphql_namespaceObject.ID)
    // _id: Types.ObjectId;
    ,
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ required: true, unique: true }),
    service_line_entity_metadata("design:type", String)
], ServiceLine.prototype, "name", void 0);
service_line_entity_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,mongoose_namespaceObject.Prop)(),
    service_line_entity_metadata("design:type", String)
], ServiceLine.prototype, "description", void 0);
service_line_entity_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,mongoose_namespaceObject.Prop)({ default: true }),
    service_line_entity_metadata("design:type", Boolean)
], ServiceLine.prototype, "isActive", void 0);
service_line_entity_decorate([
    (0,graphql_namespaceObject.Field)(),
    service_line_entity_metadata("design:type", typeof (service_line_entity_a = typeof Date !== "undefined" && Date) === "function" ? service_line_entity_a : Object)
], ServiceLine.prototype, "createdAt", void 0);
service_line_entity_decorate([
    (0,graphql_namespaceObject.Field)(),
    service_line_entity_metadata("design:type", typeof (service_line_entity_b = typeof Date !== "undefined" && Date) === "function" ? service_line_entity_b : Object)
], ServiceLine.prototype, "updatedAt", void 0);
ServiceLine = service_line_entity_decorate([
    (0,graphql_namespaceObject.ObjectType)(),
    (0,mongoose_namespaceObject.Schema)({ timestamps: true })
], ServiceLine);

const ServiceLineSchema = mongoose_namespaceObject.SchemaFactory.createForClass(ServiceLine);

;// external "bcryptjs"
const external_bcryptjs_namespaceObject = require("bcryptjs");
;// external "crypto"
const external_crypto_namespaceObject = require("crypto");
;// external "nodemailer"
const external_nodemailer_namespaceObject = require("nodemailer");
;// ../../libs/email/src/lib/services/email.service.ts
var email_service_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var email_service_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};


let EmailService = class EmailService {
    transporter;
    constructor() {
        this.transporter = external_nodemailer_namespaceObject.createTransport({
            host: process.env.SMTP_HOST || 'localhost',
            port: parseInt(process.env.SMTP_PORT || '587'),
            secure: process.env.SMTP_SECURE === 'true',
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS,
            },
        });
    }
    async sendOtpEmail(email, otpCode) {
        await this.transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: 'OTP Verification - Healthcare Assessment',
            html: `
        <h2>Email Verification</h2>
        <p>Your OTP code is: <strong>${otpCode}</strong></p>
        <p>This code will expire in 10 minutes.</p>
      `,
        });
    }
    async sendWelcomeEmail(email, fullName) {
        await this.transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: 'Welcome to Healthcare Assessment',
            html: `
        <h2>Welcome ${fullName}!</h2>
        <p>Your registration has been completed successfully.</p>
        <p>You can now sign in to your account.</p>
      `,
        });
    }
    async sendPasswordResetEmail(email, resetToken) {
        const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
        await this.transporter.sendMail({
            from: process.env.FROM_EMAIL || '<EMAIL>',
            to: email,
            subject: 'Password Reset - Healthcare Assessment',
            html: `
        <h2>Password Reset Request</h2>
        <p>Click the link below to reset your password:</p>
        <a href="${resetUrl}">Reset Password</a>
        <p>This link will expire in 30 minutes.</p>
      `,
        });
    }
};
EmailService = email_service_decorate([
    (0,common_namespaceObject.Injectable)(),
    email_service_metadata("design:paramtypes", [])
], EmailService);


;// ../../libs/email/src/lib/email.module.ts
var email_module_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};


let EmailModule = class EmailModule {
};
EmailModule = email_module_decorate([
    (0,common_namespaceObject.Module)({
        providers: [EmailService],
        exports: [EmailService],
    })
], EmailModule);


;// ../../libs/email/src/index.ts



;// ./src/utils/otp.util.ts
function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

;// ./src/services/auth.service.ts
var auth_service_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var auth_service_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var auth_service_a, auth_service_b, auth_service_c, auth_service_d, auth_service_e;











let AuthService = class AuthService {
    userModel;
    facilityTypeModel;
    serviceLineModel;
    jwtService;
    emailService;
    constructor(userModel, facilityTypeModel, serviceLineModel, jwtService, emailService) {
        this.userModel = userModel;
        this.facilityTypeModel = facilityTypeModel;
        this.serviceLineModel = serviceLineModel;
        this.jwtService = jwtService;
        this.emailService = emailService;
    }
    async signUp(signUpInput) {
        const { email, facilityType, ...userData } = signUpInput;
        // Check if user already exists
        const existingUser = await this.userModel.findOne({ email });
        if (existingUser) {
            throw new common_namespaceObject.ConflictException('User with this email already exists');
        }
        // Validate facility type
        const facilityTypeDoc = await this.facilityTypeModel.findOne({ name: facilityType, isActive: true });
        if (!facilityTypeDoc) {
            throw new common_namespaceObject.BadRequestException('Invalid facility type');
        }
        // Generate OTP
        const otpCode = generateOTP();
        const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
        // Create user
        const user = new this.userModel({
            ...userData,
            email,
            facilityType: facilityTypeDoc._id,
            status: UserStatus.PENDING_VERIFICATION,
            otpCode,
            otpExpires,
        });
        await user.save();
        // Send OTP email
        await this.emailService.sendOtpEmail(email, otpCode);
        return {
            success: true,
            message: 'Registration initiated. Please check your email for OTP verification.',
            nextStep: 'verify_otp'
        };
    }
    async verifyOtp(verifyOtpInput) {
        const { email, otpCode } = verifyOtpInput;
        const user = await this.userModel.findOne({ email });
        if (!user) {
            throw new common_namespaceObject.BadRequestException('User not found');
        }
        if (user.otpCode !== otpCode || user.otpExpires < new Date()) {
            throw new common_namespaceObject.BadRequestException('Invalid or expired OTP');
        }
        // Clear OTP and update status
        user.otpCode = undefined;
        user.otpExpires = undefined;
        user.status = UserStatus.PENDING_PASSWORD;
        await user.save();
        return {
            success: true,
            message: 'Email verified successfully. Please set your password.',
            nextStep: 'set_password'
        };
    }
    async setPassword(setPasswordInput) {
        const { email, password } = setPasswordInput;
        const user = await this.userModel.findOne({ email });
        if (!user || user.status !== UserStatus.PENDING_PASSWORD) {
            throw new common_namespaceObject.BadRequestException('Invalid request');
        }
        // Hash password
        const hashedPassword = await external_bcryptjs_namespaceObject.hash(password, 12);
        user.password = hashedPassword;
        user.status = UserStatus.ACTIVE;
        await user.save();
        // Send welcome email
        await this.emailService.sendWelcomeEmail(user.email, user.fullName);
        return {
            success: true,
            message: 'Registration completed successfully. You can now sign in.',
            nextStep: 'complete'
        };
    }
    async setHospitalDetails(hospitalDetailsInput) {
        const { email, facilityName, facilityType, state, county, numberOfLicensedBeds, serviceLines } = hospitalDetailsInput;
        // Find the user
        const user = await this.userModel.findOne({ email });
        if (!user) {
            throw new common_namespaceObject.BadRequestException('User not found');
        }
        // Validate facility type
        const facilityTypeDoc = await this.facilityTypeModel.findOne({ name: facilityType, isActive: true });
        if (!facilityTypeDoc) {
            throw new common_namespaceObject.BadRequestException('Invalid facility type');
        }
        // Validate service lines
        const serviceLineIds = [];
        if (serviceLines && serviceLines.length > 0) {
            for (const serviceLineName of serviceLines) {
                const serviceLineDoc = await this.serviceLineModel.findOne({ name: serviceLineName, isActive: true });
                if (!serviceLineDoc) {
                    throw new common_namespaceObject.BadRequestException(`Invalid service line: ${serviceLineName}`);
                }
                serviceLineIds.push(serviceLineDoc._id);
            }
        }
        // Update user with hospital details
        user.facilityName = facilityName;
        user.facilityType = facilityTypeDoc._id;
        user.state = state;
        user.county = county;
        user.numberOfLicensedBeds = numberOfLicensedBeds;
        user.serviceLines = serviceLineIds;
        user.status = UserStatus.PENDING_DETAILS;
        await user.save();
        return {
            success: true,
            message: 'Hospital details updated successfully.',
            nextStep: 'complete'
        };
    }
    async signIn(signInInput) {
        const { emailOrPhone, password } = signInInput;
        // Find user by email or phone
        const user = await this.userModel.findOne({
            $or: [
                { email: emailOrPhone },
                { phoneNumber: emailOrPhone }
            ]
        }).populate('facilityType');
        if (!user) {
            throw new common_namespaceObject.UnauthorizedException('Invalid credentials');
        }
        if (user.status !== UserStatus.ACTIVE) {
            throw new common_namespaceObject.UnauthorizedException('Account not activated. Please complete registration.');
        }
        // Verify password
        const isPasswordValid = await external_bcryptjs_namespaceObject.compare(password, user.password);
        if (!isPasswordValid) {
            throw new common_namespaceObject.UnauthorizedException('Invalid credentials');
        }
        // Generate JWT token
        const payload = {
            userId: user._id,
            email: user.email,
            role: user.role,
            facilityName: user.facilityName
        };
        const token = this.jwtService.sign(payload);
        return {
            success: true,
            message: 'Sign in successful',
            token,
            user
        };
    }
    async forgotPassword(forgotPasswordInput) {
        const { email } = forgotPasswordInput;
        const user = await this.userModel.findOne({ email });
        if (!user) {
            return {
                success: true,
                message: 'If an account with this email exists, a password reset link has been sent.'
            };
        }
        // Generate reset token
        const resetToken = external_crypto_namespaceObject.randomBytes(32).toString('hex');
        const resetExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
        user.resetToken = resetToken;
        user.resetTokenExpires = resetExpires;
        await user.save();
        // Send reset email
        await this.emailService.sendPasswordResetEmail(email, resetToken);
        return {
            success: true,
            message: 'If an account with this email exists, a password reset link has been sent.'
        };
    }
    async resetPassword(resetPasswordInput) {
        const { token, password } = resetPasswordInput;
        const user = await this.userModel.findOne({
            resetToken: token,
            resetTokenExpires: { $gt: new Date() }
        });
        if (!user) {
            throw new common_namespaceObject.BadRequestException('Invalid or expired reset token');
        }
        // Hash new password
        const hashedPassword = await external_bcryptjs_namespaceObject.hash(password, 12);
        user.password = hashedPassword;
        user.resetToken = undefined;
        user.resetTokenExpires = undefined;
        await user.save();
        return {
            success: true,
            message: 'Password reset successfully'
        };
    }
    async updateProfile(userId, updateProfileInput) {
        const user = await this.userModel.findByIdAndUpdate(userId, updateProfileInput, { new: true }).populate('facilityType');
        if (!user) {
            throw new common_namespaceObject.BadRequestException('User not found');
        }
        return {
            success: true,
            message: 'Profile updated successfully',
            user
        };
    }
    async getProfile(userId) {
        const user = await this.userModel.findById(userId).populate('facilityType');
        if (!user) {
            throw new common_namespaceObject.BadRequestException('User not found');
        }
        return {
            success: true,
            message: 'Profile retrieved successfully',
            user
        };
    }
};
AuthService = auth_service_decorate([
    (0,common_namespaceObject.Injectable)(),
    __param(0, (0,mongoose_namespaceObject.InjectModel)(User.name)),
    __param(1, (0,mongoose_namespaceObject.InjectModel)(FacilityType.name)),
    __param(2, (0,mongoose_namespaceObject.InjectModel)(ServiceLine.name)),
    auth_service_metadata("design:paramtypes", [typeof (auth_service_a = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? auth_service_a : Object, typeof (auth_service_b = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? auth_service_b : Object, typeof (auth_service_c = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? auth_service_c : Object, typeof (auth_service_d = typeof jwt_namespaceObject.JwtService !== "undefined" && jwt_namespaceObject.JwtService) === "function" ? auth_service_d : Object, typeof (auth_service_e = typeof EmailService !== "undefined" && EmailService) === "function" ? auth_service_e : Object])
], AuthService);


;// ./src/services/seed.service.ts
var seed_service_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var seed_service_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var seed_service_param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var seed_service_a, seed_service_b;





let SeedService = class SeedService {
    facilityTypeModel;
    serviceLineModel;
    constructor(facilityTypeModel, serviceLineModel) {
        this.facilityTypeModel = facilityTypeModel;
        this.serviceLineModel = serviceLineModel;
    }
    async onModuleInit() {
        await this.seedFacilityTypes();
        await this.seedServiceLines();
    }
    async seedFacilityTypes() {
        const count = await this.facilityTypeModel.countDocuments();
        if (count === 0) {
            const facilityTypes = [
                { name: 'Hospital', description: 'General Hospital' },
                { name: 'Clinic', description: 'Medical Clinic' },
                { name: 'Nursing Home', description: 'Long-term Care Facility' },
                { name: 'Rehabilitation Center', description: 'Rehabilitation Facility' },
            ];
            await this.facilityTypeModel.insertMany(facilityTypes);
            console.log('Facility types seeded successfully');
        }
    }
    async seedServiceLines() {
        const count = await this.serviceLineModel.countDocuments();
        if (count === 0) {
            const serviceLines = [
                { name: 'Emergency Medicine', description: 'Emergency care services' },
                { name: 'Cardiology', description: 'Heart and cardiovascular care' },
                { name: 'Orthopedics', description: 'Bone and joint care' },
                { name: 'Neurology', description: 'Brain and nervous system care' },
                { name: 'Pediatrics', description: 'Children healthcare' },
                { name: 'Oncology', description: 'Cancer treatment and care' },
            ];
            await this.serviceLineModel.insertMany(serviceLines);
            console.log('Service lines seeded successfully');
        }
    }
};
SeedService = seed_service_decorate([
    (0,common_namespaceObject.Injectable)(),
    seed_service_param(0, (0,mongoose_namespaceObject.InjectModel)(FacilityType.name)),
    seed_service_param(1, (0,mongoose_namespaceObject.InjectModel)(ServiceLine.name)),
    seed_service_metadata("design:paramtypes", [typeof (seed_service_a = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? seed_service_a : Object, typeof (seed_service_b = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? seed_service_b : Object])
], SeedService);


;// ./src/guards/jwt-auth.guard.ts
var jwt_auth_guard_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var jwt_auth_guard_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var jwt_auth_guard_a;




let JwtAuthGuard = class JwtAuthGuard extends (0,passport_namespaceObject.AuthGuard)('jwt') {
    jwtService;
    constructor(jwtService) {
        super();
        this.jwtService = jwtService;
    }
    getRequest(context) {
        const ctx = graphql_namespaceObject.GqlExecutionContext.create(context);
        return ctx.getContext().req;
    }
    canActivate(context) {
        const ctx = graphql_namespaceObject.GqlExecutionContext.create(context);
        const request = ctx.getContext().req;
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            throw new common_namespaceObject.UnauthorizedException('No token provided');
        }
        try {
            const payload = this.jwtService.verify(token);
            request.user = payload;
            return true;
        }
        catch (error) {
            throw new common_namespaceObject.UnauthorizedException('Invalid token');
        }
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }
};
JwtAuthGuard = jwt_auth_guard_decorate([
    (0,common_namespaceObject.Injectable)(),
    jwt_auth_guard_metadata("design:paramtypes", [typeof (jwt_auth_guard_a = typeof jwt_namespaceObject.JwtService !== "undefined" && jwt_namespaceObject.JwtService) === "function" ? jwt_auth_guard_a : Object])
], JwtAuthGuard);


;// external "class-validator"
const external_class_validator_namespaceObject = require("class-validator");
;// ./src/dto/auth.dto.ts
var auth_dto_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var auth_dto_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var auth_dto_a;



let SignUpInput = class SignUpInput {
    fullName;
    email;
    phoneNumber;
    facilityName;
    facilityType;
    state;
    county;
    role;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "fullName", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsEmail)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "email", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "phoneNumber", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "facilityName", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "facilityType", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "state", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "county", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignUpInput.prototype, "role", void 0);
SignUpInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], SignUpInput);

let VerifyOtpInput = class VerifyOtpInput {
    email;
    otpCode;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsEmail)(),
    auth_dto_metadata("design:type", String)
], VerifyOtpInput.prototype, "email", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], VerifyOtpInput.prototype, "otpCode", void 0);
VerifyOtpInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], VerifyOtpInput);

let SetPasswordInput = class SetPasswordInput {
    email;
    password;
    confirmPassword;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsEmail)(),
    auth_dto_metadata("design:type", String)
], SetPasswordInput.prototype, "email", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    (0,external_class_validator_namespaceObject.MinLength)(8),
    auth_dto_metadata("design:type", String)
], SetPasswordInput.prototype, "password", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SetPasswordInput.prototype, "confirmPassword", void 0);
SetPasswordInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], SetPasswordInput);

let HospitalDetailsInput = class HospitalDetailsInput {
    email;
    numberOfLicensedBeds;
    serviceLines;
    facilityName;
    facilityType;
    state;
    county;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsEmail)(),
    auth_dto_metadata("design:type", String)
], HospitalDetailsInput.prototype, "email", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsNumber)(),
    auth_dto_metadata("design:type", Number)
], HospitalDetailsInput.prototype, "numberOfLicensedBeds", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(() => [String]),
    (0,external_class_validator_namespaceObject.IsArray)(),
    auth_dto_metadata("design:type", Array)
], HospitalDetailsInput.prototype, "serviceLines", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], HospitalDetailsInput.prototype, "facilityName", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], HospitalDetailsInput.prototype, "facilityType", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], HospitalDetailsInput.prototype, "state", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], HospitalDetailsInput.prototype, "county", void 0);
HospitalDetailsInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], HospitalDetailsInput);

let SignInInput = class SignInInput {
    emailOrPhone;
    password;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignInInput.prototype, "emailOrPhone", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], SignInInput.prototype, "password", void 0);
SignInInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], SignInInput);

let ForgotPasswordInput = class ForgotPasswordInput {
    email;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsEmail)(),
    auth_dto_metadata("design:type", String)
], ForgotPasswordInput.prototype, "email", void 0);
ForgotPasswordInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], ForgotPasswordInput);

let ResetPasswordInput = class ResetPasswordInput {
    token;
    password;
    confirmPassword;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], ResetPasswordInput.prototype, "token", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    (0,external_class_validator_namespaceObject.MinLength)(8),
    auth_dto_metadata("design:type", String)
], ResetPasswordInput.prototype, "password", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], ResetPasswordInput.prototype, "confirmPassword", void 0);
ResetPasswordInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], ResetPasswordInput);

let UpdateProfileInput = class UpdateProfileInput {
    fullName;
    phoneNumber;
    facilityName;
    state;
    county;
    numberOfLicensedBeds;
    serviceLines;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,external_class_validator_namespaceObject.IsOptional)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], UpdateProfileInput.prototype, "fullName", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,external_class_validator_namespaceObject.IsOptional)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], UpdateProfileInput.prototype, "phoneNumber", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,external_class_validator_namespaceObject.IsOptional)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], UpdateProfileInput.prototype, "facilityName", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,external_class_validator_namespaceObject.IsOptional)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], UpdateProfileInput.prototype, "state", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,external_class_validator_namespaceObject.IsOptional)(),
    (0,external_class_validator_namespaceObject.IsString)(),
    auth_dto_metadata("design:type", String)
], UpdateProfileInput.prototype, "county", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    (0,external_class_validator_namespaceObject.IsOptional)(),
    (0,external_class_validator_namespaceObject.IsNumber)(),
    auth_dto_metadata("design:type", Number)
], UpdateProfileInput.prototype, "numberOfLicensedBeds", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(() => [String], { nullable: true }),
    (0,external_class_validator_namespaceObject.IsOptional)(),
    (0,external_class_validator_namespaceObject.IsArray)(),
    auth_dto_metadata("design:type", Array)
], UpdateProfileInput.prototype, "serviceLines", void 0);
UpdateProfileInput = auth_dto_decorate([
    (0,graphql_namespaceObject.InputType)()
], UpdateProfileInput);

let AuthResponse = class AuthResponse {
    success;
    message;
    token;
    user;
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    auth_dto_metadata("design:type", Boolean)
], AuthResponse.prototype, "success", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    auth_dto_metadata("design:type", String)
], AuthResponse.prototype, "message", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)({ nullable: true }),
    auth_dto_metadata("design:type", String)
], AuthResponse.prototype, "token", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(() => User, { nullable: true }),
    auth_dto_metadata("design:type", typeof (auth_dto_a = typeof User !== "undefined" && User) === "function" ? auth_dto_a : Object)
], AuthResponse.prototype, "user", void 0);
AuthResponse = auth_dto_decorate([
    (0,graphql_namespaceObject.ObjectType)()
], AuthResponse);

let SignUpResponse = class SignUpResponse {
    success;
    message;
    nextStep; // 'verify_otp', 'set_password', 'hospital_details', 'complete'
};
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    auth_dto_metadata("design:type", Boolean)
], SignUpResponse.prototype, "success", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    auth_dto_metadata("design:type", String)
], SignUpResponse.prototype, "message", void 0);
auth_dto_decorate([
    (0,graphql_namespaceObject.Field)(),
    auth_dto_metadata("design:type", String)
], SignUpResponse.prototype, "nextStep", void 0);
SignUpResponse = auth_dto_decorate([
    (0,graphql_namespaceObject.ObjectType)()
], SignUpResponse);


;// ./src/resolvers/auth.resolver.ts
var auth_resolver_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var auth_resolver_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var auth_resolver_param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var auth_resolver_a, auth_resolver_b, auth_resolver_c, auth_resolver_d, auth_resolver_e, auth_resolver_f, auth_resolver_g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;





let AuthResolver = class AuthResolver {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async signUp(signUpInput) {
        return this.authService.signUp(signUpInput);
    }
    async verifyOtp(verifyOtpInput) {
        return this.authService.verifyOtp(verifyOtpInput);
    }
    async setPassword(setPasswordInput) {
        return this.authService.setPassword(setPasswordInput);
    }
    async setHospitalDetails(hospitalDetailsInput) {
        return this.authService.setHospitalDetails(hospitalDetailsInput);
    }
    async signIn(signInInput) {
        return this.authService.signIn(signInInput);
    }
    async forgotPassword(forgotPasswordInput) {
        return this.authService.forgotPassword(forgotPasswordInput);
    }
    async resetPassword(resetPasswordInput) {
        return this.authService.resetPassword(resetPasswordInput);
    }
    async updateProfile(updateProfileInput, context) {
        const userId = context.req.user?.userId;
        return this.authService.updateProfile(userId, updateProfileInput);
    }
    async getProfile(context) {
        const userId = context.req.user?.userId;
        return this.authService.getProfile(userId);
    }
};
auth_resolver_decorate([
    (0,graphql_namespaceObject.Mutation)(() => SignUpResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (auth_resolver_b = typeof SignUpInput !== "undefined" && SignUpInput) === "function" ? auth_resolver_b : Object]),
    auth_resolver_metadata("design:returntype", typeof (auth_resolver_c = typeof Promise !== "undefined" && Promise) === "function" ? auth_resolver_c : Object)
], AuthResolver.prototype, "signUp", null);
auth_resolver_decorate([
    (0,graphql_namespaceObject.Mutation)(() => SignUpResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (auth_resolver_d = typeof VerifyOtpInput !== "undefined" && VerifyOtpInput) === "function" ? auth_resolver_d : Object]),
    auth_resolver_metadata("design:returntype", typeof (auth_resolver_e = typeof Promise !== "undefined" && Promise) === "function" ? auth_resolver_e : Object)
], AuthResolver.prototype, "verifyOtp", null);
auth_resolver_decorate([
    (0,graphql_namespaceObject.Mutation)(() => SignUpResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (auth_resolver_f = typeof SetPasswordInput !== "undefined" && SetPasswordInput) === "function" ? auth_resolver_f : Object]),
    auth_resolver_metadata("design:returntype", typeof (auth_resolver_g = typeof Promise !== "undefined" && Promise) === "function" ? auth_resolver_g : Object)
], AuthResolver.prototype, "setPassword", null);
auth_resolver_decorate([
    (0,graphql_namespaceObject.Mutation)(() => SignUpResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (_h = typeof HospitalDetailsInput !== "undefined" && HospitalDetailsInput) === "function" ? _h : Object]),
    auth_resolver_metadata("design:returntype", typeof (_j = typeof Promise !== "undefined" && Promise) === "function" ? _j : Object)
], AuthResolver.prototype, "setHospitalDetails", null);
auth_resolver_decorate([
    (0,graphql_namespaceObject.Mutation)(() => AuthResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (_k = typeof SignInInput !== "undefined" && SignInInput) === "function" ? _k : Object]),
    auth_resolver_metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], AuthResolver.prototype, "signIn", null);
auth_resolver_decorate([
    (0,graphql_namespaceObject.Mutation)(() => AuthResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (_m = typeof ForgotPasswordInput !== "undefined" && ForgotPasswordInput) === "function" ? _m : Object]),
    auth_resolver_metadata("design:returntype", typeof (_o = typeof Promise !== "undefined" && Promise) === "function" ? _o : Object)
], AuthResolver.prototype, "forgotPassword", null);
auth_resolver_decorate([
    (0,graphql_namespaceObject.Mutation)(() => AuthResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (_p = typeof ResetPasswordInput !== "undefined" && ResetPasswordInput) === "function" ? _p : Object]),
    auth_resolver_metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], AuthResolver.prototype, "resetPassword", null);
auth_resolver_decorate([
    (0,common_namespaceObject.UseGuards)(JwtAuthGuard),
    (0,graphql_namespaceObject.Mutation)(() => AuthResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Args)('input')),
    auth_resolver_param(1, (0,graphql_namespaceObject.Context)()),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [typeof (_r = typeof UpdateProfileInput !== "undefined" && UpdateProfileInput) === "function" ? _r : Object, Object]),
    auth_resolver_metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], AuthResolver.prototype, "updateProfile", null);
auth_resolver_decorate([
    (0,common_namespaceObject.UseGuards)(JwtAuthGuard),
    (0,graphql_namespaceObject.Query)(() => AuthResponse),
    auth_resolver_param(0, (0,graphql_namespaceObject.Context)()),
    auth_resolver_metadata("design:type", Function),
    auth_resolver_metadata("design:paramtypes", [Object]),
    auth_resolver_metadata("design:returntype", typeof (_t = typeof Promise !== "undefined" && Promise) === "function" ? _t : Object)
], AuthResolver.prototype, "getProfile", null);
AuthResolver = auth_resolver_decorate([
    (0,graphql_namespaceObject.Resolver)(),
    auth_resolver_metadata("design:paramtypes", [typeof (auth_resolver_a = typeof AuthService !== "undefined" && AuthService) === "function" ? auth_resolver_a : Object])
], AuthResolver);


;// ./src/resolvers/user.resolver.ts
var user_resolver_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var user_resolver_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var user_resolver_param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var user_resolver_a, user_resolver_b, user_resolver_c, user_resolver_d;





let UserResolver = class UserResolver {
    facilityTypeModel;
    serviceLineModel;
    constructor(facilityTypeModel, serviceLineModel) {
        this.facilityTypeModel = facilityTypeModel;
        this.serviceLineModel = serviceLineModel;
    }
    async getFacilityTypes() {
        return this.facilityTypeModel.find({ isActive: true });
    }
    async getServiceLines() {
        return this.serviceLineModel.find({ isActive: true });
    }
};
user_resolver_decorate([
    (0,graphql_namespaceObject.Query)(() => [FacilityType]),
    user_resolver_metadata("design:type", Function),
    user_resolver_metadata("design:paramtypes", []),
    user_resolver_metadata("design:returntype", typeof (user_resolver_c = typeof Promise !== "undefined" && Promise) === "function" ? user_resolver_c : Object)
], UserResolver.prototype, "getFacilityTypes", null);
user_resolver_decorate([
    (0,graphql_namespaceObject.Query)(() => [ServiceLine]),
    user_resolver_metadata("design:type", Function),
    user_resolver_metadata("design:paramtypes", []),
    user_resolver_metadata("design:returntype", typeof (user_resolver_d = typeof Promise !== "undefined" && Promise) === "function" ? user_resolver_d : Object)
], UserResolver.prototype, "getServiceLines", null);
UserResolver = user_resolver_decorate([
    (0,graphql_namespaceObject.Resolver)(),
    user_resolver_param(0, (0,mongoose_namespaceObject.InjectModel)(FacilityType.name)),
    user_resolver_param(1, (0,mongoose_namespaceObject.InjectModel)(ServiceLine.name)),
    user_resolver_metadata("design:paramtypes", [typeof (user_resolver_a = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? user_resolver_a : Object, typeof (user_resolver_b = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? user_resolver_b : Object])
], UserResolver);


;// ./src/controllers/auth.controller.ts
var auth_controller_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var auth_controller_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var auth_controller_param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var auth_controller_a, auth_controller_b, auth_controller_c, auth_controller_d, auth_controller_e, auth_controller_f, auth_controller_g, auth_controller_h, auth_controller_j, auth_controller_k;




let AuthController = class AuthController {
    authService;
    userResolver;
    constructor(authService, userResolver) {
        this.authService = authService;
        this.userResolver = userResolver;
    }
    async signUp(signUpInput) {
        return this.authService.signUp(signUpInput);
    }
    async verifyOtp(verifyOtpInput) {
        return this.authService.verifyOtp(verifyOtpInput);
    }
    async setPassword(setPasswordInput) {
        return this.authService.setPassword(setPasswordInput);
    }
    async setHospitalDetails(hospitalDetailsInput) {
        return this.authService.setHospitalDetails(hospitalDetailsInput);
    }
    async signIn(signInInput) {
        return this.authService.signIn(signInInput);
    }
    async forgotPassword(forgotPasswordInput) {
        return this.authService.forgotPassword(forgotPasswordInput);
    }
    async resetPassword(resetPasswordInput) {
        return this.authService.resetPassword(resetPasswordInput);
    }
    async updateProfile(updateProfileInput, req) {
        return this.authService.updateProfile(req.user.userId, updateProfileInput);
    }
    async getProfile(req) {
        return this.authService.getProfile(req.user.userId);
    }
    // New APIs for facility types and service lines
    async getFacilityTypes() {
        return this.userResolver.getFacilityTypes();
    }
    async getServiceLines() {
        return this.userResolver.getServiceLines();
    }
};
auth_controller_decorate([
    (0,common_namespaceObject.Post)('signup'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_c = typeof SignUpInput !== "undefined" && SignUpInput) === "function" ? auth_controller_c : Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "signUp", null);
auth_controller_decorate([
    (0,common_namespaceObject.Post)('verify-otp'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_d = typeof VerifyOtpInput !== "undefined" && VerifyOtpInput) === "function" ? auth_controller_d : Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "verifyOtp", null);
auth_controller_decorate([
    (0,common_namespaceObject.Post)('set-password'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_e = typeof SetPasswordInput !== "undefined" && SetPasswordInput) === "function" ? auth_controller_e : Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "setPassword", null);
auth_controller_decorate([
    (0,common_namespaceObject.Post)('hospital-details'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_f = typeof HospitalDetailsInput !== "undefined" && HospitalDetailsInput) === "function" ? auth_controller_f : Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "setHospitalDetails", null);
auth_controller_decorate([
    (0,common_namespaceObject.Post)('signin'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_g = typeof SignInInput !== "undefined" && SignInInput) === "function" ? auth_controller_g : Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "signIn", null);
auth_controller_decorate([
    (0,common_namespaceObject.Post)('forgot-password'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_h = typeof ForgotPasswordInput !== "undefined" && ForgotPasswordInput) === "function" ? auth_controller_h : Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
auth_controller_decorate([
    (0,common_namespaceObject.Post)('reset-password'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_j = typeof ResetPasswordInput !== "undefined" && ResetPasswordInput) === "function" ? auth_controller_j : Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
auth_controller_decorate([
    (0,common_namespaceObject.Put)('profile'),
    auth_controller_param(0, (0,common_namespaceObject.Body)()),
    auth_controller_param(1, (0,common_namespaceObject.Request)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_k = typeof UpdateProfileInput !== "undefined" && UpdateProfileInput) === "function" ? auth_controller_k : Object, Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "updateProfile", null);
auth_controller_decorate([
    (0,common_namespaceObject.Get)('profile'),
    auth_controller_param(0, (0,common_namespaceObject.Request)()),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", [Object]),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
auth_controller_decorate([
    (0,common_namespaceObject.Get)('facility-types'),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", []),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "getFacilityTypes", null);
auth_controller_decorate([
    (0,common_namespaceObject.Get)('service-lines'),
    auth_controller_metadata("design:type", Function),
    auth_controller_metadata("design:paramtypes", []),
    auth_controller_metadata("design:returntype", Promise)
], AuthController.prototype, "getServiceLines", null);
AuthController = auth_controller_decorate([
    (0,common_namespaceObject.Controller)('auth'),
    auth_controller_metadata("design:paramtypes", [typeof (auth_controller_a = typeof AuthService !== "undefined" && AuthService) === "function" ? auth_controller_a : Object, typeof (auth_controller_b = typeof UserResolver !== "undefined" && UserResolver) === "function" ? auth_controller_b : Object])
], AuthController);


;// external "passport-jwt"
const external_passport_jwt_namespaceObject = require("passport-jwt");
;// ./src/strategies/jwt.strategy.ts
var jwt_strategy_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var jwt_strategy_metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var jwt_strategy_param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var jwt_strategy_a;






let JwtStrategy = class JwtStrategy extends (0,passport_namespaceObject.PassportStrategy)(external_passport_jwt_namespaceObject.Strategy) {
    userModel;
    constructor(userModel) {
        super({
            jwtFromRequest: external_passport_jwt_namespaceObject.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: process.env.JWT_SECRET || 'healthcare-assessment-secret',
        });
        this.userModel = userModel;
    }
    async validate(payload) {
        const user = await this.userModel.findById(payload.userId);
        if (!user) {
            throw new common_namespaceObject.UnauthorizedException('User not found');
        }
        return {
            userId: payload.userId,
            email: payload.email,
            role: payload.role,
            facilityName: payload.facilityName,
        };
    }
};
JwtStrategy = jwt_strategy_decorate([
    (0,common_namespaceObject.Injectable)(),
    jwt_strategy_param(0, (0,mongoose_namespaceObject.InjectModel)(User.name)),
    jwt_strategy_metadata("design:paramtypes", [typeof (jwt_strategy_a = typeof external_mongoose_namespaceObject.Model !== "undefined" && external_mongoose_namespaceObject.Model) === "function" ? jwt_strategy_a : Object])
], JwtStrategy);


;// ../../libs/db/src/lib/services/mongo-connection.service.ts
var mongo_connection_service_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};

let MongoConnectionService = class MongoConnectionService {
};
MongoConnectionService = mongo_connection_service_decorate([
    (0,common_namespaceObject.Injectable)()
], MongoConnectionService);


;// ../../libs/db/src/lib/database.module.ts
var database_module_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};



let DatabaseModule = class DatabaseModule {
};
DatabaseModule = database_module_decorate([
    (0,common_namespaceObject.Module)({
        imports: [
            mongoose_namespaceObject.MongooseModule.forRootAsync({
                useFactory: () => ({
                    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/healthcare-assessment',
                    connectionFactory: (connection) => {
                        connection.on('connected', () => {
                            console.log('Mongoose connected to MongoDB');
                        });
                        connection.on('error', (error) => {
                            console.error('Mongoose connection error:', error);
                        });
                        connection.on('disconnected', () => {
                            console.log('Mongoose disconnected from MongoDB');
                        });
                        return connection;
                    },
                }),
            }),
        ],
        providers: [MongoConnectionService],
        exports: [MongoConnectionService, mongoose_namespaceObject.MongooseModule],
    })
], DatabaseModule);


;// ../../libs/db/src/index.ts



;// ./src/identity.module.ts
var identity_module_decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};







// Entities and Schemas



// Services


// Resolvers


// Controllers

// Guards and Strategies


 // Import the DatabaseModule here
 // Import EmailModule
let IdentityAppModule = class IdentityAppModule {
};
IdentityAppModule = identity_module_decorate([
    (0,common_namespaceObject.Module)({
        imports: [
            DatabaseModule,
            EmailModule,
            passport_namespaceObject.PassportModule,
            graphql_namespaceObject.GraphQLModule.forRoot({
                driver: apollo_namespaceObject.ApolloDriver,
                autoSchemaFile: (0,external_path_namespaceObject.join)(process.cwd(), 'src/schema.gql'),
                sortSchema: true,
                playground: true,
                introspection: true,
                context: ({ req }) => ({ req }),
            }),
            mongoose_namespaceObject.MongooseModule.forFeature([
                { name: User.name, schema: UserSchema },
                { name: FacilityType.name, schema: FacilityTypeSchema },
                { name: ServiceLine.name, schema: ServiceLineSchema },
            ]),
            jwt_namespaceObject.JwtModule.register({
                secret: process.env.JWT_SECRET || 'healthcare-assessment-secret',
                signOptions: { expiresIn: '24h' },
            }),
        ],
        providers: [
            AuthService,
            SeedService,
            AuthResolver,
            UserResolver,
            JwtStrategy,
            JwtAuthGuard,
        ],
        controllers: [AuthController],
        exports: [AuthService, SeedService],
    })
], IdentityAppModule);


;// ./src/main.ts

 // Change IdentityModule to IdentityAppModule
async function bootstrap() {
    const app = await core_namespaceObject.NestFactory.create(IdentityAppModule);
    app.enableCors({
        origin: '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
        allowedHeaders: 'Content-Type,Authorization,orgId,subOrgId,processId,userId,roleName',
    });
    // Add simple health check endpoint
    app.use('/health', (_, res) => {
        res.status(200).json({ status: 'ok', uptime: process.uptime() });
    });
    await app.listen(process.env.PORT ?? 4001);
}
bootstrap();

var __webpack_export_target__ = exports;
for(var __webpack_i__ in __webpack_exports__) __webpack_export_target__[__webpack_i__] = __webpack_exports__[__webpack_i__];
if(__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, "__esModule", { value: true });
/******/ })()
;